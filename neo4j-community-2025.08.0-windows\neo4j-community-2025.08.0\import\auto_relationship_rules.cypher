// 自动关系推断规则

// 1. 同部门的人自动成为同事
MATCH (p1:Person), (p2:Person)
WHERE p1.department = p2.department 
  AND p1.id <> p2.id
  AND NOT EXISTS((p1)-[:COLLEAGUE]-(p2))
CREATE (p1)-[:COLLEAGUE {auto_created: true, reason: "same_department"}]->(p2);

// 2. 年龄相近的人（±3岁）可能是朋友
MATCH (p1:Person), (p2:Person)
WHERE abs(p1.age - p2.age) <= 3 
  AND p1.id <> p2.id
  AND NOT EXISTS((p1)-[:POTENTIAL_FRIEND]-(p2))
CREATE (p1)-[:POTENTIAL_FRIEND {auto_created: true, reason: "similar_age"}]->(p2);

// 3. 同城且同部门的人关系更密切
MATCH (p1:Person), (p2:Person)
WHERE p1.city = p2.city 
  AND p1.department = p2.department
  AND p1.id <> p2.id
  AND NOT EXISTS((p1)-[:CLOSE_COLLEAGUE]-(p2))
CREATE (p1)-[:CLOSE_COLLEAGUE {auto_created: true, reason: "same_city_department"}]->(p2);

// 4. 基于邮箱域名推断公司关系
MATCH (p:Person)
WHERE p.email CONTAINS "@company.com"
WITH p
MATCH (c:Company)
WHERE c.name CONTAINS "公司"
AND NOT EXISTS((p)-[:WORKS_FOR]->(c))
CREATE (p)-[:EMAIL_ASSOCIATED {auto_created: true, reason: "email_domain"}]->(c);
