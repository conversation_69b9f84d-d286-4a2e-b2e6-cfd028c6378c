Neo4j 2025.08.0
=======================================

Welcome to Neo4j release 2025.08.0, a high-performance graph database.
This is the community distribution of Neo4j, including everything you need to
start building applications that can model, persist and explore graph-like data.

In the box
----------

Neo4j runs as a server application, exposing a Web-based management interface.

Here in the installation directory, you'll find:

* bin - scripts and other executables
* conf - server configuration
* data - databases
* lib - libraries
* plugins - user extensions
* logs - log files
* import - location of files for LOAD CSV

Make it go
----------

For full instructions, see https://neo4j.com/docs/operations-manual/current/installation/

To get started with Neo4j, let's start the server and take a
look at the web interface ...

1. Open a console and navigate to the installation directory.
2. Start the server:
   * Windows, use: bin\neo4j-admin server console
   * Linux/Mac, use: ./bin/neo4j-admin server console
3. In a browser, open http://localhost:7474/
4. Shutdown the server by typing Ctrl-C in the console.

Learn more
----------

* Neo4j Home: https://neo4j.com/
* Getting Started: https://neo4j.com/docs/developer-manual/current/introduction/
* Neo4j Documentation: https://neo4j.com/docs/

License(s)
----------
Various licenses apply. Please refer to the LICENSE and NOTICE files for more
detailed information.
