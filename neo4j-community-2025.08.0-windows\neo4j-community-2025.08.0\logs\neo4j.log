2025-09-22 08:18:41.298+0000 INFO  Logging config in use: File 'E:\neo4j\neo4j-community-2025.08.0-windows\neo4j-community-2025.08.0\conf\user-logs.xml'
2025-09-22 08:18:41.311+0000 INFO  Starting...
2025-09-22 08:18:42.383+0000 INFO  This instance is ServerId{b35704b1} (b35704b1-c3a8-499a-ba0c-4ffa7fac8f09)
2025-09-22 08:18:44.113+0000 INFO  ======== Neo4j 2025.08.0 ========
2025-09-22 08:18:46.606+0000 INFO  Anonymous Usage Data is being sent to Neo4j, see https://neo4j.com/docs/usage-data/
2025-09-22 08:18:47.675+0000 INFO  Bolt enabled on localhost:7687.
2025-09-22 08:18:48.902+0000 INFO  HTTP enabled on localhost:7474.
2025-09-22 08:18:48.903+0000 INFO  Remote interface available at http://localhost:7474/
2025-09-22 08:18:48.907+0000 INFO  id: 0218C4F89E5A67861172F8AC28DC672FE65E55995A506B20293349DA824C4F54
2025-09-22 08:18:48.908+0000 INFO  name: system
2025-09-22 08:18:48.909+0000 INFO  creationDate: 2025-09-22T08:18:45.653Z
2025-09-22 08:18:48.909+0000 INFO  Started.
2025-09-22 08:20:15.457+0000 INFO  Logging config in use: File 'E:\neo4j\neo4j-community-2025.08.0-windows\neo4j-community-2025.08.0\conf\user-logs.xml'
2025-09-22 08:20:15.480+0000 INFO  Starting...
2025-09-22 08:20:17.099+0000 INFO  This instance is ServerId{b35704b1} (b35704b1-c3a8-499a-ba0c-4ffa7fac8f09)
2025-09-22 08:20:19.483+0000 INFO  ======== Neo4j 2025.08.0 ========
2025-09-22 08:20:23.712+0000 INFO  Anonymous Usage Data is being sent to Neo4j, see https://neo4j.com/docs/usage-data/
2025-09-22 08:20:24.156+0000 INFO  Bolt enabled on localhost:7687.
2025-09-22 08:20:25.639+0000 INFO  HTTP enabled on localhost:7474.
2025-09-22 08:20:25.639+0000 INFO  Remote interface available at http://localhost:7474/
2025-09-22 08:20:25.649+0000 INFO  id: 0218C4F89E5A67861172F8AC28DC672FE65E55995A506B20293349DA824C4F54
2025-09-22 08:20:25.650+0000 INFO  name: system
2025-09-22 08:20:25.651+0000 INFO  creationDate: 2025-09-22T08:18:45.653Z
2025-09-22 08:20:25.652+0000 INFO  Started.
2025-09-22 11:01:53.423+0000 WARN  [bolt-56] The client is unauthorized due to authentication failure.
2025-09-22 11:02:04.040+0000 WARN  [bolt-57] The client is unauthorized due to authentication failure.
2025-09-22 11:02:05.337+0000 WARN  [bolt-58] The client is unauthorized due to authentication failure.
2025-09-22 11:02:06.510+0000 WARN  [bolt-59] The client has provided incorrect authentication details too many times in a row.
2025-09-22 11:02:07.693+0000 WARN  [bolt-60] Unsupported authentication token, missing key `credentials`
2025-09-22 12:30:13.961+0000 INFO  Logging config in use: File 'E:\neo4j\neo4j-community-2025.08.0-windows\neo4j-community-2025.08.0\conf\user-logs.xml'
2025-09-22 12:30:13.979+0000 INFO  Starting...
2025-09-22 12:30:14.994+0000 INFO  This instance is ServerId{b35704b1} (b35704b1-c3a8-499a-ba0c-4ffa7fac8f09)
2025-09-22 12:30:16.881+0000 INFO  ======== Neo4j 2025.08.0 ========
2025-09-22 12:30:27.906+0000 ERROR Failed to start Neo4j on localhost:7474.
java.lang.RuntimeException: Error starting Neo4j database server at E:\neo4j\neo4j-community-2025.08.0-windows\neo4j-community-2025.08.0\data\databases
	at org.neo4j.graphdb.facade.DatabaseManagementServiceFactory.startDatabaseServer(DatabaseManagementServiceFactory.java:293) ~[neo4j-2025.08.0.jar:2025.08.0]
	at org.neo4j.graphdb.facade.DatabaseManagementServiceFactory.build(DatabaseManagementServiceFactory.java:227) ~[neo4j-2025.08.0.jar:2025.08.0]
	at org.neo4j.server.CommunityBootstrapper.createNeo(CommunityBootstrapper.java:39) ~[neo4j-2025.08.0.jar:2025.08.0]
	at org.neo4j.server.NeoBootstrapper.start(NeoBootstrapper.java:192) [neo4j-2025.08.0.jar:2025.08.0]
	at org.neo4j.server.NeoBootstrapper.start(NeoBootstrapper.java:102) [neo4j-2025.08.0.jar:2025.08.0]
	at org.neo4j.server.Neo4jCommunity.main(Neo4jCommunity.java:30) [neo4j-2025.08.0.jar:2025.08.0]
Caused by: org.neo4j.kernel.lifecycle.LifecycleException: Component 'org.neo4j.kernel.internal.locker.LockerLifecycleAdapter@64a896b0' was successfully initialized, but failed to start. Please see the attached cause exception "Lock file has been locked by another process: E:\neo4j\neo4j-community-2025.08.0-windows\neo4j-community-2025.08.0\data\databases\store_lock. Please ensure no other process is using this database, and that the directory is writable (required even for read-only access)".
	at org.neo4j.kernel.lifecycle.LifeSupport$LifecycleInstance.start(LifeSupport.java:364) ~[neo4j-common-2025.08.0.jar:2025.08.0]
	at org.neo4j.kernel.lifecycle.LifeSupport.start(LifeSupport.java:91) ~[neo4j-common-2025.08.0.jar:2025.08.0]
	at org.neo4j.graphdb.facade.DatabaseManagementServiceFactory.startDatabaseServer(DatabaseManagementServiceFactory.java:284) ~[neo4j-2025.08.0.jar:2025.08.0]
	... 5 more
Caused by: org.neo4j.io.locker.FileLockException: Lock file has been locked by another process: E:\neo4j\neo4j-community-2025.08.0-windows\neo4j-community-2025.08.0\data\databases\store_lock. Please ensure no other process is using this database, and that the directory is writable (required even for read-only access)
	at org.neo4j.io.locker.Locker.storeLockException(Locker.java:150) ~[neo4j-io-2025.08.0.jar:2025.08.0]
	at org.neo4j.io.locker.Locker.checkLock(Locker.java:82) ~[neo4j-io-2025.08.0.jar:2025.08.0]
	at org.neo4j.kernel.internal.locker.GlobalFileLocker.checkLock(GlobalFileLocker.java:55) ~[neo4j-kernel-2025.08.0.jar:2025.08.0]
	at org.neo4j.kernel.internal.locker.GlobalLocker.checkLock(GlobalLocker.java:28) ~[neo4j-kernel-2025.08.0.jar:2025.08.0]
	at org.neo4j.kernel.internal.locker.LockerLifecycleAdapter.start(LockerLifecycleAdapter.java:34) ~[neo4j-kernel-2025.08.0.jar:2025.08.0]
	at org.neo4j.kernel.lifecycle.LifeSupport$LifecycleInstance.start(LifeSupport.java:347) ~[neo4j-common-2025.08.0.jar:2025.08.0]
	at org.neo4j.kernel.lifecycle.LifeSupport.start(LifeSupport.java:91) ~[neo4j-common-2025.08.0.jar:2025.08.0]
	at org.neo4j.graphdb.facade.DatabaseManagementServiceFactory.startDatabaseServer(DatabaseManagementServiceFactory.java:284) ~[neo4j-2025.08.0.jar:2025.08.0]
	... 5 more
2025-09-22 12:30:27.915+0000 INFO  Neo4j Server shutdown initiated by request
2025-09-22 12:30:27.916+0000 INFO  Stopped.
