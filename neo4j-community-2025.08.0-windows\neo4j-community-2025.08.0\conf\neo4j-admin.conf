#********************************************************************
# Memory Settings
#********************************************************************
#
# Since the memory requirements of administration tasks generally differs from
# the ones of the DBMS, it is recommended to provide an administration-task-specific
# memory configuration.
#
# Memory settings are specified kibibytes with the 'k' suffix, mebibytes with
# 'm' and gibibytes with 'g'.
#
# Java Heap Size: If not explicitly set in neo4j.conf, the Java heap size is dynamically
# calculated based on available system resources. Uncomment these lines to set specific
# initial and maximum heap size.
#server.memory.heap.initial_size=512m
#server.memory.heap.max_size=512m
#
# The amount of memory to use for mapping the store files.
# If not explicitly set in any of the configuration files, the default page cache memory
# assumes the machine is dedicated to running Neo4j, and is heuristically set to 50% of RAM minus
# the Java heap size.
#server.memory.pagecache.size=10g


#********************************************************************
# JVM Parameters
#********************************************************************

# Parallel GC as throughput-oriented GC is ideal for administration tasks.
server.jvm.additional=-XX:+UseParallelGC

# Have common exceptions keep producing stack traces, so they can be
# debugged regardless of how often logs are rotated.
server.jvm.additional=-XX:-OmitStackTraceInFastThrow

# Trust that non-static final fields are really final.
# This allows more optimizations and improves overall performance.
server.jvm.additional=-XX:+UnlockExperimentalVMOptions
server.jvm.additional=-XX:+TrustFinalNonStaticFields

# Disable explicit garbage collection, which is occasionally invoked by the JDK itself.
server.jvm.additional=-XX:+DisableExplicitGC

# Restrict size of cached JDK buffers to 1 KB
server.jvm.additional=-Djdk.nio.maxCachedBufferSize=1024

# More efficient buffer allocation in Netty by allowing direct no cleaner buffers.
server.jvm.additional=-Dio.netty.tryReflectionSetAccessible=true

# Netty resources leak detection.
# Possible values are DISABLED, SIMPLE, ADVANCED, PARANOID. Ignores case.
# For more details please refer to the netty documentation
server.jvm.additional=-Dio.netty.leakDetection.level=DISABLED

# Exits JVM on the first occurrence of an out-of-memory error. Its preferable to restart VM in case of out of memory errors.
server.jvm.additional=-XX:+ExitOnOutOfMemoryError

# Expand Diffie Hellman (DH) key size from default 1024 to 2048 for DH-RSA cipher suites used in server TLS handshakes.
# This is to protect the server from any potential passive eavesdropping.
server.jvm.additional=-Djdk.tls.ephemeralDHKeySize=2048

# Enable remote debugging
#server.jvm.additional=-agentlib:jdwp=transport=dt_socket,server=y,suspend=n,address=*:5005

# This filter prevents deserialization of arbitrary objects via java object serialization, addressing potential vulnerabilities.
# By default this filter whitelists all neo4j classes, as well as classes from the hazelcast library and the java standard library.
# These defaults should only be modified by expert users!
# For more details (including filter syntax) see: https://openjdk.java.net/jeps/290
#server.jvm.additional=-Djdk.serialFilter=java.**;org.neo4j.**;com.neo4j.**;com.hazelcast.**;net.sf.ehcache.Element;com.sun.proxy.*;org.openjdk.jmh.**;!*

# Increase the default flight recorder stack sampling depth from 64 to 256, to avoid truncating frames when profiling.
server.jvm.additional=-XX:FlightRecorderOptions=stackdepth=256

# Allow profilers to sample between safepoints. Without this, sampling profilers may produce less accurate results.
server.jvm.additional=-XX:+UnlockDiagnosticVMOptions
server.jvm.additional=-XX:+DebugNonSafepoints

# Open modules for neo4j to allow internal access
server.jvm.additional=--add-opens=java.base/java.nio=ALL-UNNAMED
server.jvm.additional=--add-opens=java.base/java.io=ALL-UNNAMED
server.jvm.additional=--add-opens=java.base/sun.nio.ch=ALL-UNNAMED
server.jvm.additional=--add-opens=java.base/java.util.concurrent=ALL-UNNAMED

# Enable native memory access
server.jvm.additional=--enable-native-access=ALL-UNNAMED

# Enable access to JDK vector API
# server.jvm.additional=--add-modules=jdk.incubator.vector

# Disable logging JMX endpoint.
server.jvm.additional=-Dlog4j2.disable.jmx=true
