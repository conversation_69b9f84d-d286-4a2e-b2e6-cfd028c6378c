[{"id": 1, "name": "<PERSON>", "profile": {"age": 28, "interests": ["编程", "阅读", "旅行"], "location": "北京"}, "connections": [{"friend_id": 2, "relationship": "best_friend", "strength": 0.9}, {"friend_id": 3, "relationship": "colleague", "strength": 0.7}]}, {"id": 2, "name": "<PERSON>", "profile": {"age": 32, "interests": ["游戏", "编程", "音乐"], "location": "上海"}, "connections": [{"friend_id": 1, "relationship": "best_friend", "strength": 0.9}, {"friend_id": 4, "relationship": "roommate", "strength": 0.8}]}, {"id": 3, "name": "<PERSON>", "profile": {"age": 25, "interests": ["设计", "艺术", "旅行"], "location": "深圳"}, "connections": [{"friend_id": 1, "relationship": "colleague", "strength": 0.7}]}, {"id": 4, "name": "<PERSON>", "profile": {"age": 29, "interests": ["音乐", "舞蹈", "健身"], "location": "上海"}, "connections": [{"friend_id": 2, "relationship": "roommate", "strength": 0.8}]}]