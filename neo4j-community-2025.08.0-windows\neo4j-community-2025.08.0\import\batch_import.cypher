// 批量导入和自动关系推断脚本

// 第一步：导入基础数据
LOAD CSV WITH HEADERS FROM 'file:///people.csv' AS row
CREATE (p:Person {
  id: toInteger(row.id),
  name: row.name,
  age: toInteger(row.age),
  city: row.city,
  department: row.department,
  email: row.email
});

// 第二步：创建索引以提高查询性能
CREATE INDEX person_id_index FOR (p:Person) ON (p.id);
CREATE INDEX person_name_index FOR (p:Person) ON (p.name);
CREATE INDEX person_city_index FOR (p:Person) ON (p.city);
CREATE INDEX person_department_index FOR (p:Person) ON (p.department);

// 第三步：自动推断关系
// 同部门关系
MATCH (p1:Person), (p2:Person)
WHERE p1.department = p2.department AND p1.id <> p2.id
CREATE (p1)-[:COLLEAGUE {auto_created: true}]->(p2);

// 同城关系
MATCH (p1:Person), (p2:Person)
WHERE p1.city = p2.city AND p1.id <> p2.id
CREATE (p1)-[:SAME_CITY {auto_created: true}]->(p2);

// 年龄相近关系
MATCH (p1:Person), (p2:Person)
WHERE abs(p1.age - p2.age) <= 5 AND p1.id <> p2.id
CREATE (p1)-[:SIMILAR_AGE {age_diff: abs(p1.age - p2.age)}]->(p2);

// 第四步：创建统计信息
MATCH (p:Person)
WITH p.city as city, count(p) as person_count
CREATE (cs:CityStats {city: city, population: person_count});

MATCH (p:Person)
WITH p.department as dept, count(p) as dept_count
CREATE (ds:DepartmentStats {department: dept, size: dept_count});
