{"time":"2025-09-22 08:18:41.852+0000","level":"INFO","category":"o.n.g.f.m.GlobalModule","message":"Logging config in use: File 'E:\\neo4j\\neo4j-community-2025.08.0-windows\\neo4j-community-2025.08.0\\conf\\server-logs.xml'"}
{"time":"2025-09-22 08:18:41.995+0000","level":"WARN","category":"o.n.k.i.JvmChecker","message":"The max heap memory has not been configured. It is recommended that it is always explicitly configured, to ensure the system has a balanced configuration. Until then, a JVM computed heuristic of 4261412864 bytes is used instead. If you are running neo4j server, you need to configure server.memory.heap.max_size in neo4j.conf. If you are running neo4j embedded, you have to launch the JVM with -Xmx set to a value. You can run neo4j-admin server memory-recommendation for memory configuration suggestions."}
{"time":"2025-09-22 08:18:41.996+0000","level":"WARN","category":"o.n.k.i.JvmChecker","message":"The initial heap memory has not been configured. It is recommended that it is always explicitly configured, to ensure the system has a balanced configuration. Until then, a JVM computed heuristic of 266338304 bytes is used instead. If you are running neo4j server, you need to configure server.memory.heap.initial_size in neo4j.conf. If you are running neo4j embedded, you have to launch the JVM with -Xms set to a value. You can run neo4j-admin server memory-recommendation for memory configuration suggestions."}
{"time":"2025-09-22 08:18:42.122+0000","level":"WARN","category":"o.n.i.p.PageCache","message":"The server.memory.pagecache.size setting has not been configured. It is recommended that this setting is always explicitly configured, to ensure the system has a balanced configuration. Until then, a computed heuristic value of 6390712320 bytes will be used instead. Run `neo4j-admin memory-recommendation` for memory configuration suggestions."}
{"time":"2025-09-22 08:18:42.382+0000","level":"INFO","category":"o.n.d.i.DefaultIdentityModule","message":"Generated new ServerId: ServerId{b35704b1} (b35704b1-c3a8-499a-ba0c-4ffa7fac8f09)"}
{"time":"2025-09-22 08:18:42.383+0000","level":"INFO","category":"o.n.d.i.DefaultIdentityModule","message":"This instance is ServerId{b35704b1} (b35704b1-c3a8-499a-ba0c-4ffa7fac8f09)"}
{"time":"2025-09-22 08:18:44.114+0000","level":"INFO","category":"o.n.s.CommunityNeoWebServer","message":"======== Neo4j 2025.08.0 ========"}
{"time":"2025-09-22 08:18:44.276+0000","level":"INFO","category":"o.n.d.d.DatabaseLifecycles","message":"Creating 'DatabaseId{00000000[system]}'."}
{"time":"2025-09-22 08:18:44.277+0000","level":"INFO","category":"o.n.i.d.DiagnosticsManager","message":"\r\n                                                                ********************************************************************************\r\n                                                                                             [ System diagnostics ]                             \r\n                                                                ********************************************************************************"}
{"time":"2025-09-22 08:18:44.277+0000","level":"INFO","category":"o.n.i.d.DiagnosticsManager","message":"\r\n                                                                --------------------------------------------------------------------------------\r\n                                                                                         [ System memory information ]                          \r\n                                                                --------------------------------------------------------------------------------\r\n                                                                Total Physical memory: 15.87GiB\r\n                                                                Free Physical memory: 2.086GiB\r\n                                                                Committed virtual memory: 332.7MiB\r\n                                                                Total swap space: 30.87GiB\r\n                                                                Free swap space: 8.647GiB\r\n                                                                "}
{"time":"2025-09-22 08:18:44.277+0000","level":"INFO","category":"o.n.i.d.DiagnosticsManager","message":"\r\n                                                                --------------------------------------------------------------------------------\r\n                                                                                           [ JVM memory information ]                           \r\n                                                                --------------------------------------------------------------------------------\r\n                                                                Free  memory: 64.48MiB\r\n                                                                Total memory: 100.0MiB\r\n                                                                Max   memory: 3.969GiB\r\n                                                                Garbage Collector: G1 Young Generation: [G1 Eden Space, G1 Survivor Space, G1 Old Gen]\r\n                                                                Garbage Collector: G1 Concurrent GC: [G1 Old Gen]\r\n                                                                Garbage Collector: G1 Old Generation: [G1 Eden Space, G1 Survivor Space, G1 Old Gen]\r\n                                                                Memory Pool: CodeHeap 'non-nmethods' (Non-heap memory): committed=3.313MiB, used=3.266MiB, max=7.313MiB, threshold=0B\r\n                                                                Memory Pool: Metaspace (Non-heap memory): committed=49.06MiB, used=48.41MiB, max=-1B, threshold=0B\r\n                                                                Memory Pool: CodeHeap 'profiled nmethods' (Non-heap memory): committed=8.750MiB, used=8.581MiB, max=116.3MiB, threshold=0B\r\n                                                                Memory Pool: Compressed Class Space (Non-heap memory): committed=6.188MiB, used=5.920MiB, max=1.000GiB, threshold=0B\r\n                                                                Memory Pool: G1 Eden Space (Heap memory): committed=56.00MiB, used=2.000MiB, max=-1B, threshold=?\r\n                                                                Memory Pool: G1 Old Gen (Heap memory): committed=38.00MiB, used=27.59MiB, max=3.969GiB, threshold=0B\r\n                                                                Memory Pool: G1 Survivor Space (Heap memory): committed=6.000MiB, used=5.478MiB, max=-1B, threshold=?\r\n                                                                Memory Pool: CodeHeap 'non-profiled nmethods' (Non-heap memory): committed=2.625MiB, used=2.578MiB, max=116.4MiB, threshold=0B\r\n                                                                "}
{"time":"2025-09-22 08:18:44.278+0000","level":"INFO","category":"o.n.i.d.DiagnosticsManager","message":"\r\n                                                                --------------------------------------------------------------------------------\r\n                                                                                        [ Operating system information ]                        \r\n                                                                --------------------------------------------------------------------------------\r\n                                                                Operating System: Windows 10; version: 10.0; arch: amd64; cpus: 16\r\n                                                                Process id: 22724\r\n                                                                Byte order: LITTLE_ENDIAN\r\n                                                                Local timezone: Asia/Shanghai\r\n                                                                Memory page size: 4096\r\n                                                                Unaligned memory access allowed: true\r\n                                                                "}
{"time":"2025-09-22 08:18:44.278+0000","level":"INFO","category":"o.n.i.d.DiagnosticsManager","message":"\r\n                                                                --------------------------------------------------------------------------------\r\n                                                                                              [ JVM information ]                               \r\n                                                                --------------------------------------------------------------------------------\r\n                                                                VM Name: OpenJDK 64-Bit Server VM\r\n                                                                VM Vendor: Eclipse Adoptium\r\n                                                                VM Version: 21.0.8+9-LTS\r\n                                                                JIT compiler: HotSpot 64-Bit Tiered Compilers\r\n                                                                VM Arguments: [-XX:+UseG1GC, -XX:-OmitStackTraceInFastThrow, -XX:+AlwaysPreTouch, -XX:+UnlockExperimentalVMOptions, -XX:+TrustFinalNonStaticFields, -XX:+DisableExplicitGC, -Djdk.nio.maxCachedBufferSize=1024, -Dio.netty.tryReflectionSetAccessible=true, -Dio.netty.leakDetection.level=DISABLED, -Djdk.tls.ephemeralDHKeySize=2048, -Djdk.tls.rejectClientInitiatedRenegotiation=true, -XX:FlightRecorderOptions=stackdepth=256, -XX:+UnlockDiagnosticVMOptions, -XX:+DebugNonSafepoints, --add-opens=java.base/java.nio=ALL-UNNAMED, --add-opens=java.base/java.io=ALL-UNNAMED, --add-opens=java.base/sun.nio.ch=ALL-UNNAMED, --add-opens=java.base/java.util.concurrent=ALL-UNNAMED, --enable-native-access=ALL-UNNAMED, -Dlog4j2.disable.jmx=true, -Dlog4j.layout.jsonTemplate.maxStringLength=32768, -Dfile.encoding=UTF-8]\r\n                                                                "}
{"time":"2025-09-22 08:18:44.278+0000","level":"INFO","category":"o.n.i.d.DiagnosticsManager","message":"\r\n\r\n                                                                --------------------------------------------------------------------------------\r\n                                                                                               [ Java classpath ]                               \r\n                                                                --------------------------------------------------------------------------------\r\n                                                                 [classpath] E:\\neo4j\\neo4j-community-2025.08.0-windows\\neo4j-community-2025.08.0\\lib\\scala-reflect-2.13.16.jar\r\n                                                                 [classpath] E:\\neo4j\\neo4j-community-2025.08.0-windows\\neo4j-community-2025.08.0\\lib\\neo4j-consistency-check-2025.08.0.jar\r\n                                                                 [classpath] E:\\neo4j\\neo4j-community-2025.08.0-windows\\neo4j-community-2025.08.0\\lib\\listenablefuture-9999.0-empty-to-avoid-conflict-with-guava.jar\r\n                                                                 [classpath] E:\\neo4j\\neo4j-community-2025.08.0-windows\\neo4j-community-2025.08.0\\lib\\neo4j-java-driver-6.0.0-alpha03.jar\r\n                                                                 [classpath] E:\\neo4j\\neo4j-community-2025.08.0-windows\\neo4j-community-2025.08.0\\lib\\jctools-core-4.0.5.jar\r\n                                                                 [classpath] E:\\neo4j\\neo4j-community-2025.08.0-windows\\neo4j-community-2025.08.0\\lib\\guava-33.4.8-jre.jar\r\n                                                                 [classpath] E:\\neo4j\\neo4j-community-2025.08.0-windows\\neo4j-community-2025.08.0\\lib\\neo4j-cypher-cache-2025.08.0.jar\r\n                                                                 [classpath] E:\\neo4j\\neo4j-community-2025.08.0-windows\\neo4j-community-2025.08.0\\lib\\grpc-protobuf-1.73.0.jar\r\n                                                                 [classpath] E:\\neo4j\\neo4j-community-2025.08.0-windows\\neo4j-community-2025.08.0\\lib\\neo4j-diagnostics-2025.08.0.jar\r\n                                                                 [classpath] E:\\neo4j\\neo4j-community-2025.08.0-windows\\neo4j-community-2025.08.0\\lib\\neo4j-expressions-2025.08.0.jar\r\n                                                                 [classpath] E:\\neo4j\\neo4j-community-2025.08.0-windows\\neo4j-community-2025.08.0\\lib\\neo4j-collections-2025.08.0.jar\r\n                                                                 [classpath] E:\\neo4j\\neo4j-community-2025.08.0-windows\\neo4j-community-2025.08.0\\lib\\neo4j-ast-2025.08.0.jar\r\n                                                                 [classpath] E:\\neo4j\\neo4j-community-2025.08.0-windows\\neo4j-community-2025.08.0\\lib\\cypher-parser-ast-common-2025.08.0.jar\r\n                                                                 [classpath] E:\\neo4j\\neo4j-community-2025.08.0-windows\\neo4j-community-2025.08.0\\lib\\stax-ex-1.8.1.jar\r\n                                                                 [classpath] E:\\neo4j\\neo4j-community-2025.08.0-windows\\neo4j-community-2025.08.0\\lib\\jsr305-3.0.2.jar\r\n                                                                 [classpath] E:\\neo4j\\neo4j-community-2025.08.0-windows\\neo4j-community-2025.08.0\\lib\\neo4j-codegen-2025.08.0.jar\r\n                                                                 [classpath] E:\\neo4j\\neo4j-community-2025.08.0-windows\\neo4j-community-2025.08.0\\lib\\neo4j-exceptions-2025.08.0.jar\r\n                                                                 [classpath] E:\\neo4j\\neo4j-community-2025.08.0-windows\\neo4j-community-2025.08.0\\lib\\neo4j-capabilities-2025.08.0.jar\r\n                                                                 [classpath] E:\\neo4j\\neo4j-community-2025.08.0-windows\\neo4j-community-2025.08.0\\lib\\jetty-http2-hpack-12.0.17.jar\r\n                                                                 [classpath] E:\\neo4j\\neo4j-community-2025.08.0-windows\\neo4j-community-2025.08.0\\lib\\neo4j-data-collector-2025.08.0.jar\r\n                                                                 [classpath] E:\\neo4j\\neo4j-community-2025.08.0-windows\\neo4j-community-2025.08.0\\lib\\reactive-streams-1.0.4.jar\r\n                                                                 [classpath] E:\\neo4j\\neo4j-community-2025.08.0-windows\\neo4j-community-2025.08.0\\lib\\jaxb-api-2.2.12.jar\r\n                                                                 [classpath] E:\\neo4j\\neo4j-community-2025.08.0-windows\\neo4j-community-2025.08.0\\lib\\arrow-vector-18.3.0.jar\r\n                                                                 [classpath] E:\\neo4j\\neo4j-community-2025.08.0-windows\\neo4j-community-2025.08.0\\lib\\jetty-ee8-nested-12.0.17.jar\r\n                                                                 [classpath] E:\\neo4j\\neo4j-community-2025.08.0-windows\\neo4j-community-2025.08.0\\lib\\jakarta.activation-api-1.2.2.jar\r\n                                                                 [classpath] E:\\neo4j\\neo4j-community-2025.08.0-windows\\neo4j-community-2025.08.0\\lib\\jansi-2.4.0.jar\r\n                                                                 [classpath] E:\\neo4j\\neo4j-community-2025.08.0-windows\\neo4j-community-2025.08.0\\lib\\neo4j-util-2025.08.0.jar\r\n                                                                 [classpath] E:\\neo4j\\neo4j-community-2025.08.0-windows\\neo4j-community-2025.08.0\\lib\\neo4j-fabric-2025.08.0.jar\r\n                                                                 [classpath] E:\\neo4j\\neo4j-community-2025.08.0-windows\\neo4j-community-2025.08.0\\lib\\jakarta.annotation-api-1.3.5.jar\r\n                                                                 [classpath] E:\\neo4j\\neo4j-community-2025.08.0-windows\\neo4j-community-2025.08.0\\lib\\parquet-encoding-1.15.2.jar\r\n                                                                 [classpath] E:\\neo4j\\neo4j-community-2025.08.0-windows\\neo4j-community-2025.08.0\\lib\\jna-5.17.0.jar\r\n                                                                 [classpath] E:\\neo4j\\neo4j-community-2025.08.0-windows\\neo4j-community-2025.08.0\\lib\\jersey-server-2.43.jar\r\n                                                                 [classpath] E:\\neo4j\\neo4j-community-2025.08.0-windows\\neo4j-community-2025.08.0\\lib\\jetty-http2-server-12.0.17.jar\r\n                                                                 [classpath] E:\\neo4j\\neo4j-community-2025.08.0-windows\\neo4j-community-2025.08.0\\lib\\txw2-2.3.2.jar\r\n                                                                 [classpath] E:\\neo4j\\neo4j-community-2025.08.0-windows\\neo4j-community-2025.08.0\\lib\\shiro-lang-2.0.5.jar\r\n                                                                 [classpath] E:\\neo4j\\neo4j-community-2025.08.0-windows\\neo4j-community-2025.08.0\\lib\\neo4j-layout-2025.08.0.jar\r\n                                                                 [classpath] E:\\neo4j\\neo4j-community-2025.08.0-windows\\neo4j-community-2025.08.0\\lib\\log4j-api-2.20.0.jar\r\n                                                                 [classpath] E:\\neo4j\\neo4j-community-2025.08.0-windows\\neo4j-community-2025.08.0\\lib\\jackson-jaxrs-base-2.19.1.jar\r\n                                                                 [classpath] E:\\neo4j\\neo4j-community-2025.08.0-windows\\neo4j-community-2025.08.0\\lib\\jakarta.validation-api-2.0.2.jar\r\n                                                                 [classpath] E:\\neo4j\\neo4j-community-2025.08.0-windows\\neo4j-community-2025.08.0\\lib\\grpc-netty-1.73.0.jar\r\n                                                                 [classpath] E:\\neo4j\\neo4j-community-2025.08.0-windows\\neo4j-community-2025.08.0\\lib\\flight-core-18.3.0.jar\r\n                                                                 [classpath] E:\\neo4j\\neo4j-community-2025.08.0-windows\\neo4j-community-2025.08.0\\lib\\neo4j-native-2025.08.0.jar\r\n                                                                 [classpath] E:\\neo4j\\neo4j-community-2025.08.0-windows\\neo4j-community-2025.08.0\\lib\\commons-configuration2-2.12.0.jar\r\n                                                                 [classpath] E:\\neo4j\\neo4j-community-2025.08.0-windows\\neo4j-community-2025.08.0\\lib\\neo4j-record-storage-engine-2025.08.0.jar\r\n                                                                 [classpath] E:\\neo4j\\neo4j-community-2025.08.0-windows\\neo4j-community-2025.08.0\\lib\\neo4j-bolt-2025.08.0.jar\r\n                                                                 [classpath] E:\\neo4j\\neo4j-community-2025.08.0-windows\\neo4j-community-2025.08.0\\lib\\neo4j-2025.08.0.jar"}
{"time":"2025-09-22 08:18:44.279+0000","level":"INFO","category":"o.n.i.d.DiagnosticsManager","message":"\r\n                                                                 [classpath] E:\\neo4j\\neo4j-community-2025.08.0-windows\\neo4j-community-2025.08.0\\lib\\zstd-jni-1.5.7-4.jar\r\n                                                                 [classpath] E:\\neo4j\\neo4j-community-2025.08.0-windows\\neo4j-community-2025.08.0\\lib\\neo4j-kernel-2025.08.0.jar\r\n                                                                 [classpath] E:\\neo4j\\neo4j-community-2025.08.0-windows\\neo4j-community-2025.08.0\\lib\\asm-util-9.8.jar\r\n                                                                 [classpath] E:\\neo4j\\neo4j-community-2025.08.0-windows\\neo4j-community-2025.08.0\\lib\\neo4j-lucene-index-2025.08.0.jar\r\n                                                                 [classpath] E:\\neo4j\\neo4j-community-2025.08.0-windows\\neo4j-community-2025.08.0\\lib\\neo4j-bolt-connection-routed-6.0.2.jar\r\n                                                                 [classpath] E:\\neo4j\\neo4j-community-2025.08.0-windows\\neo4j-community-2025.08.0\\lib\\jPowerShell-3.0.jar\r\n                                                                 [classpath] E:\\neo4j\\neo4j-community-2025.08.0-windows\\neo4j-community-2025.08.0\\lib\\magnolia_2.13-1.1.8.jar\r\n                                                                 [classpath] E:\\neo4j\\neo4j-community-2025.08.0-windows\\neo4j-community-2025.08.0\\lib\\antlr4-runtime-4.13.2.jar\r\n                                                                 [classpath] E:\\neo4j\\neo4j-community-2025.08.0-windows\\neo4j-community-2025.08.0\\lib\\jline-terminal-jansi-3.21.0.jar\r\n                                                                 [classpath] E:\\neo4j\\neo4j-community-2025.08.0-windows\\neo4j-community-2025.08.0\\lib\\arrow-memory-netty-buffer-patch-18.3.0.jar\r\n                                                                 [classpath] E:\\neo4j\\neo4j-community-2025.08.0-windows\\neo4j-community-2025.08.0\\lib\\jersey-client-2.43.jar\r\n                                                                 [classpath] E:\\neo4j\\neo4j-community-2025.08.0-windows\\neo4j-community-2025.08.0\\lib\\neo4j-unsafe-2025.08.0.jar\r\n                                                                 [classpath] E:\\neo4j\\neo4j-community-2025.08.0-windows\\neo4j-community-2025.08.0\\lib\\neo4j-cypher-dsl-2024.7.1.jar\r\n                                                                 [classpath] E:\\neo4j\\neo4j-community-2025.08.0-windows\\neo4j-community-2025.08.0\\lib\\asm-tree-9.8.jar\r\n                                                                 [classpath] E:\\neo4j\\neo4j-community-2025.08.0-windows\\neo4j-community-2025.08.0\\lib\\netty-transport-native-epoll-4.2.4.Final-linux-aarch_64.jar\r\n                                                                 [classpath] E:\\neo4j\\neo4j-community-2025.08.0-windows\\neo4j-community-2025.08.0\\lib\\parquet-hadoop-1.15.2.jar\r\n                                                                 [classpath] E:\\neo4j\\neo4j-community-2025.08.0-windows\\neo4j-community-2025.08.0\\lib\\shiro-event-2.0.5.jar\r\n                                                                 [classpath] E:\\neo4j\\neo4j-community-2025.08.0-windows\\neo4j-community-2025.08.0\\lib\\neo4j-cypher-slotted-runtime-2025.08.0.jar\r\n                                                                 [classpath] E:\\neo4j\\neo4j-community-2025.08.0-windows\\neo4j-community-2025.08.0\\lib\\neo4j-cypher-planner-2025.08.0.jar\r\n                                                                 [classpath] E:\\neo4j\\neo4j-community-2025.08.0-windows\\neo4j-community-2025.08.0\\lib\\cypher-v5-ast-factory-2025.08.0.jar\r\n                                                                 [classpath] E:\\neo4j\\neo4j-community-2025.08.0-windows\\neo4j-community-2025.08.0\\lib\\cypher-v25-parser-listener-2025.08.0.jar\r\n                                                                 [classpath] E:\\neo4j\\neo4j-community-2025.08.0-windows\\neo4j-community-2025.08.0\\lib\\parquet-floor-1.57.jar\r\n                                                                 [classpath] E:\\neo4j\\neo4j-community-2025.08.0-windows\\neo4j-community-2025.08.0\\lib\\neo4j-dbms-2025.08.0.jar\r\n                                                                 [classpath] E:\\neo4j\\neo4j-community-2025.08.0-windows\\neo4j-community-2025.08.0\\lib\\jersey-container-servlet-2.43.jar\r\n                                                                 [classpath] E:\\neo4j\\neo4j-community-2025.08.0-windows\\neo4j-community-2025.08.0\\lib\\jackson-databind-2.19.1.jar\r\n                                                                 [classpath] E:\\neo4j\\neo4j-community-2025.08.0-windows\\neo4j-community-2025.08.0\\lib\\parquet-jackson-1.15.2.jar\r\n                                                                 [classpath] E:\\neo4j\\neo4j-community-2025.08.0-windows\\neo4j-community-2025.08.0\\lib\\jetty-ee8-webapp-12.0.17.jar\r\n                                                                 [classpath] E:\\neo4j\\neo4j-community-2025.08.0-windows\\neo4j-community-2025.08.0\\lib\\parquet-column-1.15.2.jar\r\n                                                                 [classpath] E:\\neo4j\\neo4j-community-2025.08.0-windows\\neo4j-community-2025.08.0\\lib\\neo4j-cypher-config-2025.08.0.jar\r\n                                                                 [classpath] E:\\neo4j\\neo4j-community-2025.08.0-windows\\neo4j-community-2025.08.0\\lib\\netty-transport-native-kqueue-4.2.4.Final-osx-aarch_64.jar\r\n                                                                 [classpath] E:\\neo4j\\neo4j-community-2025.08.0-windows\\neo4j-community-2025.08.0\\lib\\cypher-v25-ast-factory-2025.08.0.jar\r\n                                                                 [classpath] E:\\neo4j\\neo4j-community-2025.08.0-windows\\neo4j-community-2025.08.0\\lib\\neo4j-procedure-api-2025.08.0.jar\r\n                                                                 [classpath] E:\\neo4j\\neo4j-community-2025.08.0-windows\\neo4j-community-2025.08.0\\lib\\netty-codec-http2-4.2.4.Final.jar\r\n                                                                 [classpath] E:\\neo4j\\neo4j-community-2025.08.0-windows\\neo4j-community-2025.08.0\\conf\\*\r\n                                                                 [classpath] E:\\neo4j\\neo4j-community-2025.08.0-windows\\neo4j-community-2025.08.0\\lib\\arrow-format-18.3.0.jar\r\n                                                                 [classpath] E:\\neo4j\\neo4j-community-2025.08.0-windows\\neo4j-community-2025.08.0\\lib\\neo4j-token-api-2025.08.0.jar\r\n                                                                 [classpath] E:\\neo4j\\neo4j-community-2025.08.0-windows\\neo4j-community-2025.08.0\\lib\\neo4j-command-line-2025.08.0.jar\r\n                                                                 [classpath] E:\\neo4j\\neo4j-community-2025.08.0-windows\\neo4j-community-2025.08.0\\lib\\cypher-antlr-common-2025.08.0.jar\r\n                                                                 [classpath] E:\\neo4j\\neo4j-community-2025.08.0-windows\\neo4j-community-2025.08.0\\lib\\commons-logging-1.3.5.jar\r\n                                                                 [classpath] E:\\neo4j\\neo4j-community-2025.08.0-windows\\neo4j-community-2025.08.0\\lib\\neo4j-front-end-2025.08.0.jar\r\n                                                                 [classpath] E:\\neo4j\\neo4j-community-2025.08.0-windows\\neo4j-community-2025.08.0\\lib\\cypher-parser-common-2025.08.0.jar\r\n                                                                 [classpath] E:\\neo4j\\neo4j-community-2025.08.0-windows\\neo4j-community-2025.08.0\\lib\\neo4j-udc-2025.08.0.jar\r\n                                                                 [classpath] E:\\neo4j\\neo4j-community-2025.08.0-windows\\neo4j-community-2025.08.0\\lib\\grpc-util-1.73.0.jar\r\n                                                                 [classpath] E:\\neo4j\\neo4j-community-2025.08.0-windows\\neo4j-community-2025.08.0\\lib\\neo4j-cypher-planner-spi-2025.08.0.jar\r\n                                                                 [classpath] E:\\neo4j\\neo4j-community-2025.08.0-windows\\neo4j-community-2025.08.0\\lib\\jersey-hk2-2.43.jar\r\n                                                                 [classpath] E:\\neo4j\\neo4j-community-2025.08.0-windows\\neo4j-community-2025.08.0\\lib\\shiro-crypto-cipher-2.0.5.jar\r\n                                                                 [classpath] E:\\neo4j\\neo4j-community-2025.08.0-windows\\neo4j-community-2025.08.0\\lib\\jackson-datatype-jsr310-2.18.3.jar\r\n                                                                 [classpath] E:\\neo4j\\neo4j-community-2025.08.0-windows\\neo4j-community-2025.08.0\\lib\\neo4j-cypher-2025.08.0.jar\r\n                                                                 [classpath] E:\\neo4j\\neo4j-community-2025.08.0-windows\\neo4j-community-2025.08.0\\lib\\neo4j-spatial-index-2025.08.0.jar\r\n                                                                 [classpath] E:\\neo4j\\neo4j-community-2025.08.0-windows\\neo4j-community-2025.08.0\\lib\\jetty-xml-12.0.17.jar"}
{"time":"2025-09-22 08:18:44.281+0000","level":"INFO","category":"o.n.i.d.DiagnosticsManager","message":"\r\n                                                                 [classpath] E:\\neo4j\\neo4j-community-2025.08.0-windows\\neo4j-community-2025.08.0\\lib\\neo4j-bolt-connection-netty-6.0.2.jar\r\n                                                                 [classpath] E:\\neo4j\\neo4j-community-2025.08.0-windows\\neo4j-community-2025.08.0\\lib\\neo4j-values-2025.08.0.jar\r\n                                                                 [classpath] E:\\neo4j\\neo4j-community-2025.08.0-windows\\neo4j-community-2025.08.0\\lib\\netty-buffer-4.2.4.Final.jar\r\n                                                                 [classpath] E:\\neo4j\\neo4j-community-2025.08.0-windows\\neo4j-community-2025.08.0\\lib\\cypher-preparser-2025.08.0.jar\r\n                                                                 [classpath] E:\\neo4j\\neo4j-community-2025.08.0-windows\\neo4j-community-2025.08.0\\lib\\annotations-2025.08.0.jar\r\n                                                                 [classpath] E:\\neo4j\\neo4j-community-2025.08.0-windows\\neo4j-community-2025.08.0\\lib\\jetty-server-12.0.17.jar\r\n                                                                 [classpath] E:\\neo4j\\neo4j-community-2025.08.0-windows\\neo4j-community-2025.08.0\\lib\\jaxb-runtime-2.3.2.jar\r\n                                                                 [classpath] E:\\neo4j\\neo4j-community-2025.08.0-windows\\neo4j-community-2025.08.0\\lib\\neo4j-notifications-2025.08.0.jar\r\n                                                                 [classpath] E:\\neo4j\\neo4j-community-2025.08.0-windows\\neo4j-community-2025.08.0\\lib\\lucene-backward-codecs-9.11.1.jar\r\n                                                                 [classpath] E:\\neo4j\\neo4j-community-2025.08.0-windows\\neo4j-community-2025.08.0\\lib\\WMI4Java-1.6.3.jar\r\n                                                                 [classpath] E:\\neo4j\\neo4j-community-2025.08.0-windows\\neo4j-community-2025.08.0\\lib\\slf4j-api-2.0.17.jar\r\n                                                                 [classpath] E:\\neo4j\\neo4j-community-2025.08.0-windows\\neo4j-community-2025.08.0\\lib\\netty-transport-native-epoll-4.2.4.Final-linux-x86_64.jar\r\n                                                                 [classpath] E:\\neo4j\\neo4j-community-2025.08.0-windows\\neo4j-community-2025.08.0\\lib\\jackson-annotations-2.19.1.jar\r\n                                                                 [classpath] E:\\neo4j\\neo4j-community-2025.08.0-windows\\neo4j-community-2025.08.0\\lib\\hk2-locator-2.6.1.jar\r\n                                                                 [classpath] E:\\neo4j\\neo4j-community-2025.08.0-windows\\neo4j-community-2025.08.0\\lib\\neo4j-indexcommands-2025.08.0.jar\r\n                                                                 [classpath] E:\\neo4j\\neo4j-community-2025.08.0-windows\\neo4j-community-2025.08.0\\lib\\cypher-v5-parser-listener-2025.08.0.jar\r\n                                                                 [classpath] E:\\neo4j\\neo4j-community-2025.08.0-windows\\neo4j-community-2025.08.0\\lib\\jackson-jaxrs-json-provider-2.19.1.jar\r\n                                                                 [classpath] E:\\neo4j\\neo4j-community-2025.08.0-windows\\neo4j-community-2025.08.0\\lib\\reactor-core-3.7.7.jar\r\n                                                                 [classpath] E:\\neo4j\\neo4j-community-2025.08.0-windows\\neo4j-community-2025.08.0\\lib\\proto-google-common-protos-2.51.0.jar\r\n                                                                 [classpath] E:\\neo4j\\neo4j-community-2025.08.0-windows\\neo4j-community-2025.08.0\\lib\\caffeine-3.2.1.jar\r\n                                                                 [classpath] E:\\neo4j\\neo4j-community-2025.08.0-windows\\neo4j-community-2025.08.0\\lib\\netty-transport-native-unix-common-4.2.4.Final.jar\r\n                                                                 [classpath] E:\\neo4j\\neo4j-community-2025.08.0-windows\\neo4j-community-2025.08.0\\lib\\netty-transport-native-kqueue-4.2.4.Final-osx-x86_64.jar\r\n                                                                 [classpath] E:\\neo4j\\neo4j-community-2025.08.0-windows\\neo4j-community-2025.08.0\\lib\\neo4j-cypher-physical-planning-2025.08.0.jar\r\n                                                                 [classpath] E:\\neo4j\\neo4j-community-2025.08.0-windows\\neo4j-community-2025.08.0\\lib\\shiro-config-core-2.0.5.jar\r\n                                                                 [classpath] E:\\neo4j\\neo4j-community-2025.08.0-windows\\neo4j-community-2025.08.0\\lib\\jakarta.xml.bind-api-2.3.2.jar\r\n                                                                 [classpath] E:\\neo4j\\neo4j-community-2025.08.0-windows\\neo4j-community-2025.08.0\\lib\\neo4j-cypher-logical-plans-2025.08.0.jar\r\n                                                                 [classpath] E:\\neo4j\\neo4j-community-2025.08.0-windows\\neo4j-community-2025.08.0\\lib\\scala-collection-contrib_2.13-0.3.0.jar\r\n                                                                 [classpath] E:\\neo4j\\neo4j-community-2025.08.0-windows\\neo4j-community-2025.08.0\\lib\\cypher-shell-2025.08.0.jar\r\n                                                                 [classpath] E:\\neo4j\\neo4j-community-2025.08.0-windows\\neo4j-community-2025.08.0\\lib\\grpc-core-1.73.0.jar\r\n                                                                 [classpath] E:\\neo4j\\neo4j-community-2025.08.0-windows\\neo4j-community-2025.08.0\\lib\\eclipse-collections-11.1.0.jar\r\n                                                                 [classpath] E:\\neo4j\\neo4j-community-2025.08.0-windows\\neo4j-community-2025.08.0\\lib\\netty-codec-base-4.2.4.Final.jar\r\n                                                                 [classpath] E:\\neo4j\\neo4j-community-2025.08.0-windows\\neo4j-community-2025.08.0\\lib\\javax.annotation-api-1.3.2.jar\r\n                                                                 [classpath] E:\\neo4j\\neo4j-community-2025.08.0-windows\\neo4j-community-2025.08.0\\lib\\neo4j-rewriting-2025.08.0.jar\r\n                                                                 [classpath] E:\\neo4j\\neo4j-community-2025.08.0-windows\\neo4j-community-2025.08.0\\lib\\javassist-3.30.2-GA.jar\r\n                                                                 [classpath] E:\\neo4j\\neo4j-community-2025.08.0-windows\\neo4j-community-2025.08.0\\lib\\commons-compress-1.27.1.jar\r\n                                                                 [classpath] E:\\neo4j\\neo4j-community-2025.08.0-windows\\neo4j-community-2025.08.0\\lib\\neo4j-bootcheck-2025.08.0.jar\r\n                                                                 [classpath] E:\\neo4j\\neo4j-community-2025.08.0-windows\\neo4j-community-2025.08.0\\lib\\neo4j-resource-2025.08.0.jar\r\n                                                                 [classpath] E:\\neo4j\\neo4j-community-2025.08.0-windows\\neo4j-community-2025.08.0\\lib\\neo4j-cypher-runtime-util-2025.08.0.jar\r\n                                                                 [classpath] E:\\neo4j\\neo4j-community-2025.08.0-windows\\neo4j-community-2025.08.0\\lib\\commons-codec-1.18.0.jar\r\n                                                                 [classpath] E:\\neo4j\\neo4j-community-2025.08.0-windows\\neo4j-community-2025.08.0\\plugins\\*\r\n                                                                 [classpath] E:\\neo4j\\neo4j-community-2025.08.0-windows\\neo4j-community-2025.08.0\\lib\\jetty-security-12.0.17.jar\r\n                                                                 [classpath] E:\\neo4j\\neo4j-community-2025.08.0-windows\\neo4j-community-2025.08.0\\lib\\arrow-memory-netty-18.3.0.jar\r\n                                                                 [classpath] E:\\neo4j\\neo4j-community-2025.08.0-windows\\neo4j-community-2025.08.0\\lib\\netty-transport-4.2.4.Final.jar\r\n                                                                 [classpath] E:\\neo4j\\neo4j-community-2025.08.0-windows\\neo4j-community-2025.08.0\\lib\\jetty-ee8-servlet-12.0.17.jar\r\n                                                                 [classpath] E:\\neo4j\\neo4j-community-2025.08.0-windows\\neo4j-community-2025.08.0\\lib\\snappy-java-1.1.10.7.jar\r\n                                                                 [classpath] E:\\neo4j\\neo4j-community-2025.08.0-windows\\neo4j-community-2025.08.0\\lib\\jettison-1.5.4.jar\r\n                                                                 [classpath] E:\\neo4j\\neo4j-community-2025.08.0-windows\\neo4j-community-2025.08.0\\lib\\commons-lang3-3.18.0.jar\r\n                                                                 [classpath] E:\\neo4j\\neo4j-community-2025.08.0-windows\\neo4j-community-2025.08.0\\lib\\log4j-layout-template-json-2.20.0.jar\r\n                                                                 [classpath] E:\\neo4j\\neo4j-community-2025.08.0-windows\\neo4j-community-2025.08.0\\lib\\flatbuffers-java-25.2.10.jar\r\n                                                                 [classpath] E:\\neo4j\\neo4j-community-2025.08.0-windows\\neo4j-community-2025.08.0\\lib\\jetty-alpn-java-server-12.0.17.jar"}
{"time":"2025-09-22 08:18:44.282+0000","level":"INFO","category":"o.n.i.d.DiagnosticsManager","message":"\r\n                                                                 [classpath] E:\\neo4j\\neo4j-community-2025.08.0-windows\\neo4j-community-2025.08.0\\lib\\neo4j-browser-ce-2025.8.0.jar\r\n                                                                 [classpath] E:\\neo4j\\neo4j-community-2025.08.0-windows\\neo4j-community-2025.08.0\\lib\\jetty-util-12.0.17.jar\r\n                                                                 [classpath] E:\\neo4j\\neo4j-community-2025.08.0-windows\\neo4j-community-2025.08.0\\lib\\cypher-v5-literal-interpreter-2025.08.0.jar\r\n                                                                 [classpath] E:\\neo4j\\neo4j-community-2025.08.0-windows\\neo4j-community-2025.08.0\\lib\\neo4j-gql-status-2025.08.0.jar\r\n                                                                 [classpath] E:\\neo4j\\neo4j-community-2025.08.0-windows\\neo4j-community-2025.08.0\\lib\\neo4j-slf4j-provider-2025.08.0.jar\r\n                                                                 [classpath] E:\\neo4j\\neo4j-community-2025.08.0-windows\\neo4j-community-2025.08.0\\lib\\netty-transport-classes-kqueue-4.2.4.Final.jar\r\n                                                                 [classpath] E:\\neo4j\\neo4j-community-2025.08.0-windows\\neo4j-community-2025.08.0\\lib\\neo4j-graph-algo-2025.08.0.jar\r\n                                                                 [classpath] E:\\neo4j\\neo4j-community-2025.08.0-windows\\neo4j-community-2025.08.0\\lib\\jetty-alpn-server-12.0.17.jar\r\n                                                                 [classpath] E:\\neo4j\\neo4j-community-2025.08.0-windows\\neo4j-community-2025.08.0\\lib\\scala-library-2.13.16.jar\r\n                                                                 [classpath] E:\\neo4j\\neo4j-community-2025.08.0-windows\\neo4j-community-2025.08.0\\lib\\aircompressor-2.0.2.jar\r\n                                                                 [classpath] E:\\neo4j\\neo4j-community-2025.08.0-windows\\neo4j-community-2025.08.0\\lib\\cypher-antlr-ast-common-2025.08.0.jar\r\n                                                                 [classpath] E:\\neo4j\\neo4j-community-2025.08.0-windows\\neo4j-community-2025.08.0\\lib\\protobuf-java-4.31.1.jar\r\n                                                                 [classpath] E:\\neo4j\\neo4j-community-2025.08.0-windows\\neo4j-community-2025.08.0\\lib\\jakarta.ws.rs-api-2.1.6.jar\r\n                                                                 [classpath] E:\\neo4j\\neo4j-community-2025.08.0-windows\\neo4j-community-2025.08.0\\lib\\neo4j-wal-2025.08.0.jar\r\n                                                                 [classpath] E:\\neo4j\\neo4j-community-2025.08.0-windows\\neo4j-community-2025.08.0\\lib\\jersey-common-2.43.jar\r\n                                                                 [classpath] E:\\neo4j\\neo4j-community-2025.08.0-windows\\neo4j-community-2025.08.0\\lib\\neo4j-import-util-2025.08.0.jar\r\n                                                                 [classpath] E:\\neo4j\\neo4j-community-2025.08.0-windows\\neo4j-community-2025.08.0\\lib\\neo4j-kernel-api-2025.08.0.jar\r\n                                                                 [classpath] E:\\neo4j\\neo4j-community-2025.08.0-windows\\neo4j-community-2025.08.0\\lib\\shiro-hashes-bcrypt-2.0.5.jar\r\n                                                                 [classpath] E:\\neo4j\\neo4j-community-2025.08.0-windows\\neo4j-community-2025.08.0\\lib\\parquet-common-1.15.2.jar\r\n                                                                 [classpath] E:\\neo4j\\neo4j-community-2025.08.0-windows\\neo4j-community-2025.08.0\\lib\\cypher-v25-antlr-parser-2025.08.0.jar\r\n                                                                 [classpath] E:\\neo4j\\neo4j-community-2025.08.0-windows\\neo4j-community-2025.08.0\\lib\\lucene-analysis-common-9.11.1.jar\r\n                                                                 [classpath] E:\\neo4j\\neo4j-community-2025.08.0-windows\\neo4j-community-2025.08.0\\lib\\neo4j-graphdb-api-2025.08.0.jar\r\n                                                                 [classpath] E:\\neo4j\\neo4j-community-2025.08.0-windows\\neo4j-community-2025.08.0\\lib\\ipaddress-5.5.1.jar\r\n                                                                 [classpath] E:\\neo4j\\neo4j-community-2025.08.0-windows\\neo4j-community-2025.08.0\\lib\\neo4j-procedure-2025.08.0.jar\r\n                                                                 [classpath] E:\\neo4j\\neo4j-community-2025.08.0-windows\\neo4j-community-2025.08.0\\lib\\netty-resolver-4.2.4.Final.jar\r\n                                                                 [classpath] E:\\neo4j\\neo4j-community-2025.08.0-windows\\neo4j-community-2025.08.0\\lib\\neo4j-query-router-2025.08.0.jar\r\n                                                                 [classpath] E:\\neo4j\\neo4j-community-2025.08.0-windows\\neo4j-community-2025.08.0\\lib\\netty-transport-classes-epoll-4.2.4.Final.jar\r\n                                                                 [classpath] E:\\neo4j\\neo4j-community-2025.08.0-windows\\neo4j-community-2025.08.0\\lib\\shiro-core-2.0.5.jar\r\n                                                                 [classpath] E:\\neo4j\\neo4j-community-2025.08.0-windows\\neo4j-community-2025.08.0\\lib\\jetty-session-12.0.17.jar\r\n                                                                 [classpath] E:\\neo4j\\neo4j-community-2025.08.0-windows\\neo4j-community-2025.08.0\\lib\\jetty-http2-common-12.0.17.jar\r\n                                                                 [classpath] E:\\neo4j\\neo4j-community-2025.08.0-windows\\neo4j-community-2025.08.0\\lib\\jetty-io-12.0.17.jar\r\n                                                                 [classpath] E:\\neo4j\\neo4j-community-2025.08.0-windows\\neo4j-community-2025.08.0\\lib\\neo4j-lock-2025.08.0.jar\r\n                                                                 [classpath] E:\\neo4j\\neo4j-community-2025.08.0-windows\\neo4j-community-2025.08.0\\lib\\neo4j-cypher-interpreted-runtime-2025.08.0.jar\r\n                                                                 [classpath] E:\\neo4j\\neo4j-community-2025.08.0-windows\\neo4j-community-2025.08.0\\lib\\asm-analysis-9.8.jar\r\n                                                                 [classpath] E:\\neo4j\\neo4j-community-2025.08.0-windows\\neo4j-community-2025.08.0\\lib\\jackson-core-2.19.1.jar\r\n                                                                 [classpath] E:\\neo4j\\neo4j-community-2025.08.0-windows\\neo4j-community-2025.08.0\\lib\\neo4j-server-2025.08.0.jar\r\n                                                                 [classpath] E:\\neo4j\\neo4j-community-2025.08.0-windows\\neo4j-community-2025.08.0\\lib\\neo4j-index-2025.08.0.jar\r\n                                                                 [classpath] E:\\neo4j\\neo4j-community-2025.08.0-windows\\neo4j-community-2025.08.0\\lib\\server-api-2025.08.0.jar\r\n                                                                 [classpath] E:\\neo4j\\neo4j-community-2025.08.0-windows\\neo4j-community-2025.08.0\\lib\\neo4j-bolt-connection-6.0.2.jar\r\n                                                                 [classpath] E:\\neo4j\\neo4j-community-2025.08.0-windows\\neo4j-community-2025.08.0\\lib\\perfmark-api-0.27.0.jar\r\n                                                                 [classpath] E:\\neo4j\\neo4j-community-2025.08.0-windows\\neo4j-community-2025.08.0\\lib\\netty-handler-proxy-4.2.4.Final.jar\r\n                                                                 [classpath] E:\\neo4j\\neo4j-community-2025.08.0-windows\\neo4j-community-2025.08.0\\lib\\jersey-container-servlet-core-2.43.jar\r\n                                                                 [classpath] E:\\neo4j\\neo4j-community-2025.08.0-windows\\neo4j-community-2025.08.0\\lib\\shiro-hashes-argon2-2.0.5.jar\r\n                                                                 [classpath] E:\\neo4j\\neo4j-community-2025.08.0-windows\\neo4j-community-2025.08.0\\lib\\netty-codec-compression-4.2.4.Final.jar\r\n                                                                 [classpath] E:\\neo4j\\neo4j-community-2025.08.0-windows\\neo4j-community-2025.08.0\\lib\\failureaccess-1.0.3.jar\r\n                                                                 [classpath] E:\\neo4j\\neo4j-community-2025.08.0-windows\\neo4j-community-2025.08.0\\lib\\eclipse-collections-api-11.1.0.jar\r\n                                                                 [classpath] E:\\neo4j\\neo4j-community-2025.08.0-windows\\neo4j-community-2025.08.0\\lib\\neo4j-csv-2025.08.0.jar\r\n                                                                 [classpath] E:\\neo4j\\neo4j-community-2025.08.0-windows\\neo4j-community-2025.08.0\\lib\\picocli-4.7.7.jar\r\n                                                                 [classpath] E:\\neo4j\\neo4j-community-2025.08.0-windows\\neo4j-community-2025.08.0\\lib\\netty-codec-socks-4.2.4.Final.jar\r\n                                                                 [classpath] E:\\neo4j\\neo4j-community-2025.08.0-windows\\neo4j-community-2025.08.0\\lib\\neo4j-import-api-2025.08.0.jar"}
{"time":"2025-09-22 08:18:44.283+0000","level":"INFO","category":"o.n.i.d.DiagnosticsManager","message":"\r\n                                                                 [classpath] E:\\neo4j\\neo4j-community-2025.08.0-windows\\neo4j-community-2025.08.0\\lib\\protobuf-java-util-4.31.1.jar\r\n                                                                 [classpath] E:\\neo4j\\neo4j-community-2025.08.0-windows\\neo4j-community-2025.08.0\\lib\\parquet-format-structures-1.15.2.jar\r\n                                                                 [classpath] E:\\neo4j\\neo4j-community-2025.08.0-windows\\neo4j-community-2025.08.0\\lib\\jackson-module-jaxb-annotations-2.19.1.jar\r\n                                                                 [classpath] E:\\neo4j\\neo4j-community-2025.08.0-windows\\neo4j-community-2025.08.0\\lib\\neo4j-cypher-rendering-2025.08.0.jar\r\n                                                                 [classpath] E:\\neo4j\\neo4j-community-2025.08.0-windows\\neo4j-community-2025.08.0\\lib\\kiama_2.13-2.5.1.jar\r\n                                                                 [classpath] E:\\neo4j\\neo4j-community-2025.08.0-windows\\neo4j-community-2025.08.0\\lib\\shiro-crypto-hash-2.0.5.jar\r\n                                                                 [classpath] E:\\neo4j\\neo4j-community-2025.08.0-windows\\neo4j-community-2025.08.0\\lib\\grpc-context-1.73.0.jar\r\n                                                                 [classpath] E:\\neo4j\\neo4j-community-2025.08.0-windows\\neo4j-community-2025.08.0\\lib\\neo4j-concurrent-2025.08.0.jar\r\n                                                                 [classpath] E:\\neo4j\\neo4j-community-2025.08.0-windows\\neo4j-community-2025.08.0\\lib\\istack-commons-runtime-3.0.8.jar\r\n                                                                 [classpath] E:\\neo4j\\neo4j-community-2025.08.0-windows\\neo4j-community-2025.08.0\\lib\\jetty-ee-12.0.17.jar\r\n                                                                 [classpath] E:\\neo4j\\neo4j-community-2025.08.0-windows\\neo4j-community-2025.08.0\\lib\\netty-tcnative-classes-2.0.72.Final.jar\r\n                                                                 [classpath] E:\\neo4j\\neo4j-community-2025.08.0-windows\\neo4j-community-2025.08.0\\lib\\cypher-parser-factory-2025.08.0.jar\r\n                                                                 [classpath] E:\\neo4j\\neo4j-community-2025.08.0-windows\\neo4j-community-2025.08.0\\lib\\neo4j-bolt-connection-pooled-6.0.2.jar\r\n                                                                 [classpath] E:\\neo4j\\neo4j-community-2025.08.0-windows\\neo4j-community-2025.08.0\\lib\\commons-io-2.19.0.jar\r\n                                                                 [classpath] E:\\neo4j\\neo4j-community-2025.08.0-windows\\neo4j-community-2025.08.0\\lib\\shiro-crypto-core-2.0.5.jar\r\n                                                                 [classpath] E:\\neo4j\\neo4j-community-2025.08.0-windows\\neo4j-community-2025.08.0\\lib\\lucene-core-9.11.1.jar\r\n                                                                 [classpath] E:\\neo4j\\neo4j-community-2025.08.0-windows\\neo4j-community-2025.08.0\\lib\\neo4j-common-2025.08.0.jar\r\n                                                                 [classpath] E:\\neo4j\\neo4j-community-2025.08.0-windows\\neo4j-community-2025.08.0\\lib\\neo4j-cloud-2025.08.0.jar\r\n                                                                 [classpath] E:\\neo4j\\neo4j-community-2025.08.0-windows\\neo4j-community-2025.08.0\\lib\\zstd-proxy-2025.08.0.jar\r\n                                                                 [classpath] E:\\neo4j\\neo4j-community-2025.08.0-windows\\neo4j-community-2025.08.0\\lib\\hk2-utils-2.6.1.jar\r\n                                                                 [classpath] E:\\neo4j\\neo4j-community-2025.08.0-windows\\neo4j-community-2025.08.0\\lib\\neo4j-id-generator-2025.08.0.jar\r\n                                                                 [classpath] E:\\neo4j\\neo4j-community-2025.08.0-windows\\neo4j-community-2025.08.0\\lib\\netty-common-4.2.4.Final.jar\r\n                                                                 [classpath] E:\\neo4j\\neo4j-community-2025.08.0-windows\\neo4j-community-2025.08.0\\lib\\jline-terminal-3.21.0.jar\r\n                                                                 [classpath] E:\\neo4j\\neo4j-community-2025.08.0-windows\\neo4j-community-2025.08.0\\lib\\neo4j-cypher-ir-2025.08.0.jar\r\n                                                                 [classpath] E:\\neo4j\\neo4j-community-2025.08.0-windows\\neo4j-community-2025.08.0\\lib\\neo4j-configuration-2025.08.0.jar\r\n                                                                 [classpath] E:\\neo4j\\neo4j-community-2025.08.0-windows\\neo4j-community-2025.08.0\\lib\\asm-9.8.jar\r\n                                                                 [classpath] E:\\neo4j\\neo4j-community-2025.08.0-windows\\neo4j-community-2025.08.0\\lib\\lucene-queryparser-9.11.1.jar\r\n                                                                 [classpath] E:\\neo4j\\neo4j-community-2025.08.0-windows\\neo4j-community-2025.08.0\\lib\\neo4j-io-2025.08.0.jar\r\n                                                                 [classpath] E:\\neo4j\\neo4j-community-2025.08.0-windows\\neo4j-community-2025.08.0\\lib\\gson-2.11.0.jar\r\n                                                                 [classpath] E:\\neo4j\\neo4j-community-2025.08.0-windows\\neo4j-community-2025.08.0\\lib\\cypher-v5-antlr-parser-2025.08.0.jar\r\n                                                                 [classpath] E:\\neo4j\\neo4j-community-2025.08.0-windows\\neo4j-community-2025.08.0\\lib\\neo4j-push-to-cloud-2025.08.0.jar\r\n                                                                 [classpath] E:\\neo4j\\neo4j-community-2025.08.0-windows\\neo4j-community-2025.08.0\\lib\\commons-text-1.13.1.jar\r\n                                                                 [classpath] E:\\neo4j\\neo4j-community-2025.08.0-windows\\neo4j-community-2025.08.0\\lib\\argparse4j-0.9.0.jar\r\n                                                                 [classpath] E:\\neo4j\\neo4j-community-2025.08.0-windows\\neo4j-community-2025.08.0\\lib\\netty-handler-4.2.4.Final.jar\r\n                                                                 [classpath] E:\\neo4j\\neo4j-community-2025.08.0-windows\\neo4j-community-2025.08.0\\lib\\neo4j-storage-engine-util-2025.08.0.jar\r\n                                                                 [classpath] E:\\neo4j\\neo4j-community-2025.08.0-windows\\neo4j-community-2025.08.0\\lib\\jline-reader-3.21.0.jar\r\n                                                                 [classpath] E:\\neo4j\\neo4j-community-2025.08.0-windows\\neo4j-community-2025.08.0\\lib\\neo4j-cypher-macros-2025.08.0.jar\r\n                                                                 [classpath] E:\\neo4j\\neo4j-community-2025.08.0-windows\\neo4j-community-2025.08.0\\lib\\jProcesses-1.6.5.jar\r\n                                                                 [classpath] E:\\neo4j\\neo4j-community-2025.08.0-windows\\neo4j-community-2025.08.0\\lib\\log4j-core-2.20.0.jar\r\n                                                                 [classpath] E:\\neo4j\\neo4j-community-2025.08.0-windows\\neo4j-community-2025.08.0\\lib\\neo4j-import-tool-2025.08.0.jar\r\n                                                                 [classpath] E:\\neo4j\\neo4j-community-2025.08.0-windows\\neo4j-community-2025.08.0\\lib\\neo4j-cypher-expression-evaluator-2025.08.0.jar\r\n                                                                 [classpath] E:\\neo4j\\neo4j-community-2025.08.0-windows\\neo4j-community-2025.08.0\\lib\\jetty-http-12.0.17.jar\r\n                                                                 [classpath] E:\\neo4j\\neo4j-community-2025.08.0-windows\\neo4j-community-2025.08.0\\lib\\jakarta.inject-2.6.1.jar\r\n                                                                 [classpath] E:\\neo4j\\neo4j-community-2025.08.0-windows\\neo4j-community-2025.08.0\\lib\\jetty-servlet-api-4.0.6.jar\r\n                                                                 [classpath] E:\\neo4j\\neo4j-community-2025.08.0-windows\\neo4j-community-2025.08.0\\lib\\neo4j-schema-2025.08.0.jar\r\n                                                                 [classpath] E:\\neo4j\\neo4j-community-2025.08.0-windows\\neo4j-community-2025.08.0\\lib\\jetty-ee8-security-12.0.17.jar\r\n                                                                 [classpath] E:\\neo4j\\neo4j-community-2025.08.0-windows\\neo4j-community-2025.08.0\\lib\\grpc-protobuf-lite-1.73.0.jar\r\n                                                                 [classpath] E:\\neo4j\\neo4j-community-2025.08.0-windows\\neo4j-community-2025.08.0\\lib\\grpc-api-1.73.0.jar\r\n                                                                 [classpath] E:\\neo4j\\neo4j-community-2025.08.0-windows\\neo4j-community-2025.08.0\\lib\\shiro-cache-2.0.5.jar\r\n                                                                 [classpath] E:\\neo4j\\neo4j-community-2025.08.0-windows\\neo4j-community-2025.08.0\\lib\\neo4j-monitoring-2025.08.0.jar"}
{"time":"2025-09-22 08:18:44.285+0000","level":"INFO","category":"o.n.i.d.DiagnosticsManager","message":"\r\n                                                                 [classpath] E:\\neo4j\\neo4j-community-2025.08.0-windows\\neo4j-community-2025.08.0\\lib\\netty-codec-http-4.2.4.Final.jar\r\n                                                                 [classpath] E:\\neo4j\\neo4j-community-2025.08.0-windows\\neo4j-community-2025.08.0\\lib\\grpc-stub-1.73.0.jar\r\n                                                                 [classpath] E:\\neo4j\\neo4j-community-2025.08.0-windows\\neo4j-community-2025.08.0\\lib\\hk2-api-2.6.1.jar\r\n                                                                 [classpath] E:\\neo4j\\neo4j-community-2025.08.0-windows\\neo4j-community-2025.08.0\\lib\\neo4j-security-2025.08.0.jar\r\n                                                                 [classpath] E:\\neo4j\\neo4j-community-2025.08.0-windows\\neo4j-community-2025.08.0\\lib\\neo4j-logging-2025.08.0.jar\r\n                                                                 [classpath] E:\\neo4j\\neo4j-community-2025.08.0-windows\\neo4j-community-2025.08.0\\lib\\arrow-memory-core-18.3.0.jar\r\n                                                                 [classpath] E:\\neo4j\\neo4j-community-2025.08.0-windows\\neo4j-community-2025.08.0\\lib\\neo4j-ssl-2025.08.0.jar\r\n                                                                 [classpath] E:\\neo4j\\neo4j-community-2025.08.0-windows\\neo4j-community-2025.08.0\\lib\\FastInfoset-1.2.16.jar\r\n                                                                "}
{"time":"2025-09-22 08:18:44.285+0000","level":"INFO","category":"o.n.i.d.DiagnosticsManager","message":"\r\n                                                                --------------------------------------------------------------------------------\r\n                                                                                                [ Library path ]                                \r\n                                                                --------------------------------------------------------------------------------\r\n                                                                E:\\java\\bin\r\n                                                                C:\\Windows\\Sun\\Java\\bin\r\n                                                                C:\\Windows\\System32\r\n                                                                C:\\Windows\r\n                                                                E:\\java\\bin\r\n                                                                E:\\conda\r\n                                                                E:\\conda\\Library\\mingw-w64\\bin\r\n                                                                E:\\conda\\Library\\usr\\bin\r\n                                                                E:\\conda\\Library\\bin\r\n                                                                E:\\conda\\Scripts\r\n                                                                E:\\conda\\bin\r\n                                                                C:\\Windows\\System32\r\n                                                                C:\\Windows\r\n                                                                C:\\Windows\\System32\\wbem\r\n                                                                C:\\Windows\\System32\\WindowsPowerShell\\v1.0\r\n                                                                C:\\Windows\\System32\\OpenSSH\r\n                                                                C:\\Program Files\\Docker\\Docker\\resources\\bin\r\n                                                                D:\\trae\r\n                                                                D:\\node.js\r\n                                                                D:\\git\r\n                                                                D:\\git\\git\\Git\\cmd\r\n                                                                C:\\Program Files\\dotnet\r\n                                                                C:\\Users\\<USER>\\AppData\\Roaming\\npm\r\n                                                                C:\\Users\\<USER>\\AppData\\Local\\Muse Hub\\lib\r\n                                                                C:\\ProgramData\\chocolatey\\bin\r\n                                                                C:\\Program Files\\gs\\gs10.05.1\\bin\r\n                                                                C:\\ProgramData\\chocolatey\\lib\\poppler\\tools\\poppler-25.07.0\\bin\r\n                                                                C:\\Users\\<USER>\\.trae\\sdks\\workspaces\\c47c7beb\\versions\\node\\current\r\n                                                                C:\\Users\\<USER>\\.trae\\sdks\\versions\\node\\current\r\n                                                                C:\\Users\\<USER>\\.trae\\sdks\\workspaces\\c47c7beb\\versions\\node\\current\r\n                                                                C:\\Users\\<USER>\\.trae\\sdks\\versions\\node\\current\r\n                                                                C:\\Windows\\System32\r\n                                                                C:\\Windows\r\n                                                                C:\\Windows\\System32\\wbem\r\n                                                                C:\\Windows\\System32\\WindowsPowerShell\\v1.0\r\n                                                                C:\\Windows\\System32\\OpenSSH\r\n                                                                C:\\Program Files\\Docker\\Docker\\resources\\bin\r\n                                                                D:\\trae\r\n                                                                D:\\node.js\r\n                                                                D:\\git\r\n                                                                D:\\git\\git\\Git\\cmd\r\n                                                                C:\\Program Files\\dotnet\r\n                                                                C:\\Users\\<USER>\\AppData\\Roaming\\npm\r\n                                                                C:\\Users\\<USER>\\AppData\\Local\\Muse Hub\\lib\r\n                                                                C:\\ProgramData\\chocolatey\\bin\r\n                                                                C:\\Program Files\\gs\\gs10.05.1\\bin\r\n                                                                C:\\ProgramData\\chocolatey\\lib\\poppler\\tools\\poppler-25.07.0\\bin\r\n                                                                E:\\python\\Scripts\r\n                                                                E:\\python\r\n                                                                C:\\Users\\<USER>\\AppData\\Local\\Microsoft\\WindowsApps\r\n                                                                D:\\git\r\n                                                                C:\\Users\\<USER>\\AppData\\Local\\Muse Hub\\lib\r\n                                                                E:\\VSCode\\Microsoft VS Code\\bin\r\n                                                                C:\\Users\\<USER>\\AppData\\Roaming\\npm\r\n                                                                C:\\Users\\<USER>\\AppData\\Local\\Pandoc\r\n                                                                C:\\Program Files\\Tesseract-OCR\r\n                                                                C:\\ProgramData\\chocolatey\\lib\\poppler\\tools\\poppler-25.07.0\\bin\r\n                                                                E:\\kiro\\Kiro\\bin\r\n                                                                C:\\Users\\<USER>\\.trae\\extensions\\ms-python.debugpy-2025.6.0-win32-x64\\bundled\\scripts\\noConfigScripts\r\n                                                                E:\\conda\r\n                                                                E:\\conda\\Scripts\r\n                                                                E:\\conda\\condabin\r\n                                                                E:\\cursor\\cursor\\resources\\app\\bin\r\n                                                                C:\\Users\\<USER>\\AppData\\Local\\Programs\\Ollama\r\n                                                                E:\\neo4j\\neo4j-community-2025.08.0-windows\\neo4j-community-2025.08.0\r\n                                                                "}
{"time":"2025-09-22 08:18:44.286+0000","level":"INFO","category":"o.n.i.d.DiagnosticsManager","message":"\r\n                                                                --------------------------------------------------------------------------------\r\n                                                                                             [ System properties ]                              \r\n                                                                --------------------------------------------------------------------------------\r\n                                                                jdk.tls.rejectClientInitiatedRenegotiation = true\r\n                                                                sun.cpu.isalist = amd64\r\n                                                                sun.jnu.encoding = GBK\r\n                                                                log4j.layout.jsonTemplate.maxStringLength = 32768\r\n                                                                sun.arch.data.model = 64\r\n                                                                user.variant = \r\n                                                                user.timezone = Asia/Shanghai\r\n                                                                io.netty.leakDetection.level = DISABLED\r\n                                                                sun.java.launcher = SUN_STANDARD\r\n                                                                user.country = CN\r\n                                                                sun.boot.library.path = E:\\java\\bin\r\n                                                                sun.java.command = org.neo4j.server.Neo4jCommunity --home-dir=E:\\neo4j\\neo4j-community-2025.08.0-windows\\neo4j-community-2025.08.0 --config-dir=E:\\neo4j\\neo4j-community-2025.08.0-windows\\neo4j-community-2025.08.0\\conf --console-mode\r\n                                                                jdk.debug = release\r\n                                                                io.netty.tryReflectionSetAccessible = true\r\n                                                                sun.cpu.endian = little\r\n                                                                user.home = C:\\Users\\<USER>\r\n                                                                user.language = zh\r\n                                                                file.separator = \\\r\n                                                                jdk.tls.ephemeralDHKeySize = 2048\r\n                                                                user.script = \r\n                                                                sun.management.compiler = HotSpot 64-Bit Tiered Compilers\r\n                                                                user.name = Administrator\r\n                                                                stdout.encoding = ms936\r\n                                                                jdk.nio.maxCachedBufferSize = 1024\r\n                                                                path.separator = ;\r\n                                                                file.encoding = UTF-8\r\n                                                                jnidispatch.path = C:\\Users\\<USER>\\AppData\\Local\\Temp\\jna-146731693\\jna9013559238400189113.dll\r\n                                                                jna.loaded = true\r\n                                                                user.dir = E:\\neo4j\\neo4j-community-2025.08.0-windows\\neo4j-community-2025.08.0\r\n                                                                sun.os.patch.level = \r\n                                                                native.encoding = GBK\r\n                                                                stderr.encoding = ms936\r\n                                                                sun.io.unicode.encoding = UnicodeLittle\r\n                                                                log4j2.disable.jmx = true\r\n                                                                "}
{"time":"2025-09-22 08:18:44.286+0000","level":"INFO","category":"o.n.i.d.DiagnosticsManager","message":"\r\n                                                                --------------------------------------------------------------------------------\r\n                                                                                      [ (IANA) TimeZone database version ]                      \r\n                                                                --------------------------------------------------------------------------------\r\n                                                                  TimeZone version: 2025b (available for 604 zone identifiers)\r\n                                                                "}
{"time":"2025-09-22 08:18:44.286+0000","level":"INFO","category":"o.n.i.d.DiagnosticsManager","message":"\r\n                                                                --------------------------------------------------------------------------------\r\n                                                                                            [ Network information ]                             \r\n                                                                --------------------------------------------------------------------------------\r\n                                                                Interface WAN Miniport (IP)-WFP Native MAC Layer LightWeight Filter-0000:\r\n                                                                Interface WAN Miniport (IP)-QoS Packet Scheduler-0000:\r\n                                                                Interface WAN Miniport (IPv6)-WFP Native MAC Layer LightWeight Filter-0000:\r\n                                                                Interface Hyper-V Virtual Switch Extension Adapter-Hyper-V Virtual Switch Extension Filter-0000:\r\n                                                                Interface WAN Miniport (IPv6)-QoS Packet Scheduler-0000:\r\n                                                                Interface Hyper-V Virtual Ethernet Adapter-WFP Native MAC Layer LightWeight Filter-0000:\r\n                                                                Interface Hyper-V Virtual Ethernet Adapter-QoS Packet Scheduler-0000:\r\n                                                                Interface WAN Miniport (Network Monitor)-WFP Native MAC Layer LightWeight Filter-0000:\r\n                                                                Interface Realtek PCIe GbE Family Controller-WFP Native MAC Layer LightWeight Filter-0000:\r\n                                                                Interface Realtek PCIe GbE Family Controller-QoS Packet Scheduler-0000:\r\n                                                                Interface Realtek PCIe GbE Family Controller-WFP 802.3 MAC Layer LightWeight Filter-0000:\r\n                                                                Interface WAN Miniport (Network Monitor)-QoS Packet Scheduler-0000:\r\n                                                                Interface Hyper-V Virtual Ethernet Adapter-WFP 802.3 MAC Layer LightWeight Filter-0000:\r\n                                                                Interface Hyper-V Virtual Switch Extension Adapter #2-Hyper-V Virtual Switch Extension Filter-0000:\r\n                                                                Interface Hyper-V Virtual Ethernet Adapter #2-WFP Native MAC Layer LightWeight Filter-0000:\r\n                                                                Interface Hyper-V Virtual Ethernet Adapter #2-QoS Packet Scheduler-0000:\r\n                                                                Interface Hyper-V Virtual Ethernet Adapter #2-WFP 802.3 MAC Layer LightWeight Filter-0000:\r\n                                                                Interface Microsoft Kernel Debug Network Adapter:\r\n                                                                Interface Realtek PCIe GbE Family Controller:\r\n                                                                    address: fe80:0:0:0:c25a:79:75e8:6d90%ethernet_32769\r\n                                                                Interface Bluetooth Device (Personal Area Network):\r\n                                                                Interface WAN Miniport (IP):\r\n                                                                Interface WAN Miniport (IPv6):\r\n                                                                Interface WAN Miniport (Network Monitor):\r\n                                                                Interface Hyper-V Virtual Switch Extension Adapter:\r\n                                                                Interface Hyper-V Virtual Ethernet Adapter:\r\n                                                                    address: fe80:0:0:0:1a68:e38c:f878:e658%ethernet_32775\r\n                                                                    address: *************\r\n                                                                Interface Hyper-V Virtual Switch Extension Adapter #2:\r\n                                                                Interface Hyper-V Virtual Ethernet Adapter #2:\r\n                                                                    address: fe80:0:0:0:4929:732b:f43f:aa4c%ethernet_32777\r\n                                                                    address: ************\r\n                                                                Interface WAN Miniport (PPPOE):\r\n                                                                Interface Software Loopback Interface 1:\r\n                                                                    address: 0:0:0:0:0:0:0:1\r\n                                                                    address: 127.0.0.1\r\n                                                                Interface Meta Tunnel:\r\n                                                                    address: fdfe:dcba:9876:0:0:0:0:1\r\n                                                                    address: **********\r\n                                                                Interface Intel(R) Wi-Fi 6 AX200 160MHz-WFP Native MAC Layer LightWeight Filter-0000:\r\n                                                                Interface Intel(R) Wi-Fi 6 AX200 160MHz-Virtual WiFi Filter Driver-0000:\r\n                                                                Interface Intel(R) Wi-Fi 6 AX200 160MHz-Native WiFi Filter Driver-0000:\r\n                                                                Interface Intel(R) Wi-Fi 6 AX200 160MHz-QoS Packet Scheduler-0000:\r\n                                                                Interface Intel(R) Wi-Fi 6 AX200 160MHz-WFP 802.3 MAC Layer LightWeight Filter-0000:\r\n                                                                Interface Microsoft Wi-Fi Direct Virtual Adapter-WFP Native MAC Layer LightWeight Filter-0000:\r\n                                                                Interface Microsoft Wi-Fi Direct Virtual Adapter-Native WiFi Filter Driver-0000:\r\n                                                                Interface Microsoft Wi-Fi Direct Virtual Adapter-QoS Packet Scheduler-0000:\r\n                                                                Interface Microsoft Wi-Fi Direct Virtual Adapter-WFP 802.3 MAC Layer LightWeight Filter-0000:\r\n                                                                Interface Microsoft Wi-Fi Direct Virtual Adapter #2-WFP Native MAC Layer LightWeight Filter-0000:\r\n                                                                Interface Microsoft Wi-Fi Direct Virtual Adapter #2-Native WiFi Filter Driver-0000:\r\n                                                                Interface Microsoft Wi-Fi Direct Virtual Adapter #2-QoS Packet Scheduler-0000:\r\n                                                                Interface Microsoft Wi-Fi Direct Virtual Adapter #2-WFP 802.3 MAC Layer LightWeight Filter-0000:\r\n                                                                Interface Intel(R) Wi-Fi 6 AX200 160MHz:\r\n                                                                    address: fe80:0:0:0:592e:4e99:26e4:b1f5%wireless_32768\r\n                                                                    address: 2408:820c:820a:1760:9ea1:537b:5307:24e\r\n                                                                    address: 2408:820c:820a:1760:206d:eb1e:620:c820\r\n                                                                    address: 2408:820c:820a:1760:0:0:0:6fa\r\n                                                                    address: ************\r\n                                                                Interface Microsoft Wi-Fi Direct Virtual Adapter:\r\n                                                                    address: fe80:0:0:0:dcb:7296:b3b8:d60b%wireless_32769\r\n                                                                Interface Microsoft Wi-Fi Direct Virtual Adapter #2:\r\n                                                                    address: fe80:0:0:0:11f0:af32:4c68:7d36%wireless_32770\r\n                                                                Interface Microsoft Teredo Tunneling Adapter:\r\n                                                                Interface Microsoft IP-HTTPS Platform Adapter:\r\n                                                                Interface Microsoft 6to4 Adapter:\r\n                                                                Interface WAN Miniport (SSTP):\r\n                                                                Interface WAN Miniport (IKEv2):\r\n                                                                Interface WAN Miniport (L2TP):\r\n                                                                Interface WAN Miniport (PPTP):\r\n                                                                "}
{"time":"2025-09-22 08:18:44.286+0000","level":"INFO","category":"o.n.i.d.DiagnosticsManager","message":"\r\n                                                                --------------------------------------------------------------------------------\r\n                                                                                         [ Native access information ]                          \r\n                                                                --------------------------------------------------------------------------------\r\n                                                                Native access details: Native access is not available for current platform.\r\n                                                                "}
{"time":"2025-09-22 08:18:44.287+0000","level":"INFO","category":"o.n.i.d.DiagnosticsManager","message":"\r\n                                                                --------------------------------------------------------------------------------\r\n                                                                                                [ DBMS config ]                                 \r\n                                                                --------------------------------------------------------------------------------\r\n                                                                DBMS provided settings:\r\n                                                                internal.dbms.bolt.local_address=734833d9-bdc7-465c-98bd-9cd837bd86f4\r\n                                                                server.bolt.enabled=true\r\n                                                                server.directories.configuration=E:\\neo4j\\neo4j-community-2025.08.0-windows\\neo4j-community-2025.08.0\\conf\r\n                                                                server.directories.import=E:\\neo4j\\neo4j-community-2025.08.0-windows\\neo4j-community-2025.08.0\\import\r\n                                                                server.directories.neo4j_home=E:\\neo4j\\neo4j-community-2025.08.0-windows\\neo4j-community-2025.08.0\r\n                                                                server.http.enabled=true\r\n                                                                server.https.enabled=false\r\n                                                                server.jvm.additional=-XX:+UseG1GC\r\n-XX:-OmitStackTraceInFastThrow\r\n-XX:+AlwaysPreTouch\r\n-XX:+UnlockExperimentalVMOptions\r\n-XX:+TrustFinalNonStaticFields\r\n-XX:+DisableExplicitGC\r\n-Djdk.nio.maxCachedBufferSize=1024\r\n-Dio.netty.tryReflectionSetAccessible=true\r\n-Dio.netty.leakDetection.level=DISABLED\r\n-Djdk.tls.ephemeralDHKeySize=2048\r\n-Djdk.tls.rejectClientInitiatedRenegotiation=true\r\n-XX:FlightRecorderOptions=stackdepth=256\r\n-XX:+UnlockDiagnosticVMOptions\r\n-XX:+DebugNonSafepoints\r\n--add-opens=java.base/java.nio=ALL-UNNAMED\r\n--add-opens=java.base/java.io=ALL-UNNAMED\r\n--add-opens=java.base/sun.nio.ch=ALL-UNNAMED\r\n--add-opens=java.base/java.util.concurrent=ALL-UNNAMED\r\n--enable-native-access=ALL-UNNAMED\r\n-Dlog4j2.disable.jmx=true\r\n-Dlog4j.layout.jsonTemplate.maxStringLength=32768\r\n                                                                server.windows_service_name=neo4j\r\n                                                                Directories in use:\r\n                                                                server.directories.configuration=E:\\neo4j\\neo4j-community-2025.08.0-windows\\neo4j-community-2025.08.0\\conf\r\n                                                                server.directories.data=E:\\neo4j\\neo4j-community-2025.08.0-windows\\neo4j-community-2025.08.0\\data\r\n                                                                server.directories.dumps.root=E:\\neo4j\\neo4j-community-2025.08.0-windows\\neo4j-community-2025.08.0\\data\\dumps\r\n                                                                server.directories.import=E:\\neo4j\\neo4j-community-2025.08.0-windows\\neo4j-community-2025.08.0\\import\r\n                                                                server.directories.lib=E:\\neo4j\\neo4j-community-2025.08.0-windows\\neo4j-community-2025.08.0\\lib\r\n                                                                server.directories.licenses=E:\\neo4j\\neo4j-community-2025.08.0-windows\\neo4j-community-2025.08.0\\licenses\r\n                                                                server.directories.logs=E:\\neo4j\\neo4j-community-2025.08.0-windows\\neo4j-community-2025.08.0\\logs\r\n                                                                server.directories.plugins=E:\\neo4j\\neo4j-community-2025.08.0-windows\\neo4j-community-2025.08.0\\plugins\r\n                                                                server.directories.run=E:\\neo4j\\neo4j-community-2025.08.0-windows\\neo4j-community-2025.08.0\\run\r\n                                                                server.directories.script.root=E:\\neo4j\\neo4j-community-2025.08.0-windows\\neo4j-community-2025.08.0\\data\\scripts\r\n                                                                server.directories.transaction.logs.root=E:\\neo4j\\neo4j-community-2025.08.0-windows\\neo4j-community-2025.08.0\\data\\transactions\r\n                                                                "}
{"time":"2025-09-22 08:18:44.287+0000","level":"INFO","category":"o.n.i.d.DiagnosticsManager","message":"\r\n                                                                --------------------------------------------------------------------------------\r\n                                                                                                 [ Packaging ]                                  \r\n                                                                --------------------------------------------------------------------------------\r\n                                                                Edition: Community\r\n                                                                Package Type: tar\r\n                                                                "}
{"time":"2025-09-22 08:18:44.287+0000","level":"INFO","category":"o.n.i.d.DiagnosticsManager","message":"\r\n                                                                --------------------------------------------------------------------------------\r\n                                                                                           [ Global Server Identity ]                           \r\n                                                                --------------------------------------------------------------------------------\r\n                                                                Registered ServerId{b35704b1}\r\n                                                                "}
{"time":"2025-09-22 08:18:44.602+0000","level":"INFO","category":"o.n.b.BoltServer","message":"Using connector transport NIO"}
{"time":"2025-09-22 08:18:44.817+0000","level":"INFO","category":"o.n.b.BoltServer","message":"Configured external Bolt connector with listener address localhost/127.0.0.1:7687"}
{"time":"2025-09-22 08:18:44.820+0000","level":"INFO","category":"o.n.b.BoltServer","message":"Bolt server loaded"}
{"time":"2025-09-22 08:18:44.848+0000","level":"INFO","category":"o.n.d.d.DatabaseLifecycles","message":"Starting 'DatabaseId{00000000[system]}'."}
{"time":"2025-09-22 08:18:45.515+0000","level":"INFO","category":"o.n.k.d.Database","message":"[system/00000000] Current KernelVersion=KernelVersion{V2025_08,version=24}, LogFormat= V9","databaseId":"00000000-0000-0000-0000-000000000001","databaseName":"system"}
{"time":"2025-09-22 08:18:45.956+0000","level":"INFO","category":"o.n.i.d.DiagnosticsManager","message":"[system/00000000] \r\n                                                                [system/00000000] ********************************************************************************\r\n                                                                [system/00000000]                               [ Database: system ]                              \r\n                                                                [system/00000000] ********************************************************************************\r\n                                                                [system/00000000] --------------------------------------------------------------------------------\r\n                                                                [system/00000000]                                   [ Version ]                                   \r\n                                                                [system/00000000] --------------------------------------------------------------------------------\r\n                                                                [system/00000000] DBMS: community record-aligned-1.1\r\n                                                                [system/00000000] Kernel version: 2025.08.0\r\n                                                                [system/00000000] \r\n                                                                [system/00000000] --------------------------------------------------------------------------------\r\n                                                                [system/00000000]                                 [ Store files ]                                 \r\n                                                                [system/00000000] --------------------------------------------------------------------------------\r\n                                                                [system/00000000] Disk space on partition (Total / Free / Free %): 512108785664 / 17340510208 / 3\r\n                                                                [system/00000000] Storage files stored on file store: NTFS\r\n                                                                [system/00000000] Storage files: (filename : modification date - size)\r\n                                                                [system/00000000]   neostore: 2025-09-22 08:18:45.660+0000 - 8.000KiB\r\n                                                                [system/00000000]   neostore.counts.db: 2025-09-22 08:18:45.697+0000 - 0B\r\n                                                                [system/00000000]   neostore.indexstats.db: 2025-09-22 08:18:45.723+0000 - 0B\r\n                                                                [system/00000000]   neostore.labeltokenstore.db: 2025-09-22 08:18:45.643+0000 - 0B\r\n                                                                [system/00000000]   neostore.labeltokenstore.db.id: 2025-09-22 08:18:45.643+0000 - 0B\r\n                                                                [system/00000000]   neostore.labeltokenstore.db.names: 2025-09-22 08:18:45.640+0000 - 8.000KiB\r\n                                                                [system/00000000]   neostore.labeltokenstore.db.names.id: 2025-09-22 08:18:45.638+0000 - 0B\r\n                                                                [system/00000000]   neostore.nodestore.db: 2025-09-22 08:18:45.614+0000 - 0B\r\n                                                                [system/00000000]   neostore.nodestore.db.id: 2025-09-22 08:18:45.614+0000 - 0B\r\n                                                                [system/00000000]   neostore.nodestore.db.labels: 2025-09-22 08:18:45.608+0000 - 8.000KiB\r\n                                                                [system/00000000]   neostore.nodestore.db.labels.id: 2025-09-22 08:18:45.578+0000 - 0B\r\n                                                                [system/00000000]   neostore.propertystore.db: 2025-09-22 08:18:45.630+0000 - 0B\r\n                                                                [system/00000000]   neostore.propertystore.db.arrays: 2025-09-22 08:18:45.626+0000 - 8.000KiB\r\n                                                                [system/00000000]   neostore.propertystore.db.arrays.id: 2025-09-22 08:18:45.625+0000 - 0B\r\n                                                                [system/00000000]   neostore.propertystore.db.id: 2025-09-22 08:18:45.629+0000 - 0B\r\n                                                                [system/00000000]   neostore.propertystore.db.index: 2025-09-22 08:18:45.618+0000 - 0B\r\n                                                                [system/00000000]   neostore.propertystore.db.index.id: 2025-09-22 08:18:45.618+0000 - 0B\r\n                                                                [system/00000000]   neostore.propertystore.db.index.keys: 2025-09-22 08:18:45.616+0000 - 8.000KiB\r\n                                                                [system/00000000]   neostore.propertystore.db.index.keys.id: 2025-09-22 08:18:45.615+0000 - 0B\r\n                                                                [system/00000000]   neostore.propertystore.db.strings: 2025-09-22 08:18:45.618+0000 - 8.000KiB\r\n                                                                [system/00000000]   neostore.propertystore.db.strings.id: 2025-09-22 08:18:45.618+0000 - 0B\r\n                                                                [system/00000000]   neostore.relationshipgroupstore.db: 2025-09-22 08:18:45.650+0000 - 8.000KiB\r\n                                                                [system/00000000]   neostore.relationshipgroupstore.db.id: 2025-09-22 08:18:45.649+0000 - 0B\r\n                                                                [system/00000000]   neostore.relationshipgroupstore.degrees.db: 2025-09-22 08:18:45.718+0000 - 0B\r\n                                                                [system/00000000]   neostore.relationshipstore.db: 2025-09-22 08:18:45.633+0000 - 0B\r\n                                                                [system/00000000]   neostore.relationshipstore.db.id: 2025-09-22 08:18:45.632+0000 - 0B\r\n                                                                [system/00000000]   neostore.relationshiptypestore.db: 2025-09-22 08:18:45.637+0000 - 0B\r\n                                                                [system/00000000]   neostore.relationshiptypestore.db.id: 2025-09-22 08:18:45.637+0000 - 0B\r\n                                                                [system/00000000]   neostore.relationshiptypestore.db.names: 2025-09-22 08:18:45.634+0000 - 8.000KiB\r\n                                                                [system/00000000]   neostore.relationshiptypestore.db.names.id: 2025-09-22 08:18:45.633+0000 - 0B\r\n                                                                [system/00000000]   neostore.schemastore.db: 2025-09-22 08:18:45.646+0000 - 8.000KiB\r\n                                                                [system/00000000]   neostore.schemastore.db.id: 2025-09-22 08:18:45.645+0000 - 0B\r\n                                                                [system/00000000] Storage summary: \r\n                                                                [system/00000000]   Total size of store: 72.00KiB\r\n                                                                [system/00000000]   Total size of mapped files: 72.00KiB\r\n                                                                [system/00000000] \r\n                                                                [system/00000000] --------------------------------------------------------------------------------\r\n                                                                [system/00000000]                               [ Transaction log ]                               \r\n                                                                [system/00000000] --------------------------------------------------------------------------------\r\n                                                                [system/00000000] Transaction log files stored on file store: NTFS\r\n                                                                [system/00000000] Transaction log metadata:\r\n                                                                [system/00000000]  - current kernel version used in transactions: V2025_08\r\n                                                                [system/00000000]  - last committed transaction id: 1\r\n                                                                [system/00000000] Transaction log files:\r\n                                                                [system/00000000]  - existing transaction log versions: -1--1\r\n                                                                [system/00000000]  - no transactions found\r\n                                                                [system/00000000] Checkpoint log files:\r\n                                                                [system/00000000]  - existing checkpoint log versions: -1--1\r\n                                                                [system/00000000]  - no checkpoints found\r\n                                                                [system/00000000] \r\n                                                                [system/00000000] --------------------------------------------------------------------------------\r\n                                                                [system/00000000]                                   [ Id usage ]                                  \r\n                                                                [system/00000000] --------------------------------------------------------------------------------\r\n                                                                [system/00000000] ArrayPropertyStore[neostore.nodestore.db.labels]: used=1 high=0\r\n                                                                [system/00000000] NodeStore[neostore.nodestore.db]: used=0 high=-1\r\n                                                                [system/00000000] StringPropertyStore[neostore.propertystore.db.index.keys]: used=1 high=0\r\n                                                                [system/00000000] PropertyIndexStore[neostore.propertystore.db.index]: used=0 high=-1\r\n                                                                [system/00000000] StringPropertyStore[neostore.propertystore.db.strings]: used=1 high=0\r\n                                                                [system/00000000] ArrayPropertyStore[neostore.propertystore.db.arrays]: used=1 high=0\r\n                                                                [system/00000000] PropertyStore[neostore.propertystore.db]: used=0 high=-1\r\n                                                                [system/00000000] RelationshipStore[neostore.relationshipstore.db]: used=0 high=-1\r\n                                                                [system/00000000] StringPropertyStore[neostore.relationshiptypestore.db.names]: used=1 high=0\r\n                                                                [system/00000000] RelationshipTypeStore[neostore.relationshiptypestore.db]: used=0 high=-1\r\n                                                                [system/00000000] StringPropertyStore[neostore.labeltokenstore.db.names]: used=1 high=0\r\n                                                                [system/00000000] LabelTokenStore[neostore.labeltokenstore.db]: used=0 high=-1\r\n                                                                [system/00000000] SchemaStore[neostore.schemastore.db]: used=1 high=0\r\n                                                                [system/00000000] RelationshipGroupStore[neostore.relationshipgroupstore.db]: used=1 high=0\r\n                                                                [system/00000000] NeoStore[neostore]: used=-1 high=-1\r\n                                                                [system/00000000] \r\n                                                                [system/00000000] --------------------------------------------------------------------------------\r\n                                                                [system/00000000]                                   [ Metadata ]                                  \r\n                                                                [system/00000000] --------------------------------------------------------------------------------\r\n                                                                [system/00000000] EXTERNAL_STORE_UUID (Database identifier exposed as external store identity. Generated on creation and never updated): 05332ae1-329f-4a71-a6d1-************\r\n                                                                [system/00000000] DATABASE_ID (The last used DatabaseId for this database): null\r\n                                                                [system/00000000] LEGACY_STORE_VERSION (Legacy store format version. This field is used from 5.0 onwards only to distinguish non-migrated pre 5.0 metadata stores.): -3523014627327384477\r\n                                                                [system/00000000] STORE_ID (Store ID): StoreId{creationTime=1758529125653, random=-3618506131647564689, storageEngineName='record', formatName='aligned', majorVersion=1, minorVersion=1}\r\n                                                                [system/00000000] "}
{"time":"2025-09-22 08:18:45.982+0000","level":"INFO","category":"o.n.k.a.DatabaseAvailabilityGuard","message":"[system/00000000] Requirement `Database unavailable` makes database system unavailable.","databaseId":"00000000-0000-0000-0000-000000000001","databaseName":"system"}
{"time":"2025-09-22 08:18:45.983+0000","level":"INFO","category":"o.n.k.a.DatabaseAvailabilityGuard","message":"[system/00000000] DatabaseId{00000000[system]} is unavailable.","databaseId":"00000000-0000-0000-0000-000000000001","databaseName":"system"}
{"time":"2025-09-22 08:18:45.998+0000","level":"WARN","category":"o.n.k.i.s.MetaDataStore","message":"[system/00000000] Missing counts store, rebuilding it.","databaseId":"00000000-0000-0000-0000-000000000001","databaseName":"system"}
{"time":"2025-09-22 08:18:46.009+0000","level":"WARN","category":"o.n.k.i.s.MetaDataStore","message":"[system/00000000] Counts store rebuild completed.","databaseId":"00000000-0000-0000-0000-000000000001","databaseName":"system"}
{"time":"2025-09-22 08:18:46.018+0000","level":"INFO","category":"o.n.k.d.Database","message":"[system/00000000] Starting transaction log [E:\\neo4j\\neo4j-community-2025.08.0-windows\\neo4j-community-2025.08.0\\data\\transactions\\system\\neostore.transaction.db.0] at version=0","databaseId":"00000000-0000-0000-0000-000000000001","databaseName":"system"}
{"time":"2025-09-22 08:18:46.033+0000","level":"INFO","category":"o.n.k.d.Database","message":"[system/00000000] Starting transaction log [E:\\neo4j\\neo4j-community-2025.08.0-windows\\neo4j-community-2025.08.0\\data\\transactions\\system\\checkpoint.0] at version=0","databaseId":"00000000-0000-0000-0000-000000000001","databaseName":"system"}
{"time":"2025-09-22 08:18:46.037+0000","level":"INFO","category":"o.n.k.i.t.l.f.c.CheckpointLogFile","message":"[system/00000000] Scanning log file with version 0 for checkpoint entries","databaseId":"00000000-0000-0000-0000-000000000001","databaseName":"system"}
{"time":"2025-09-22 08:18:46.053+0000","level":"INFO","category":"o.n.k.a.DatabaseAvailabilityGuard","message":"[system/00000000] Fulfilling of requirement 'Database unavailable' makes database system available.","databaseId":"00000000-0000-0000-0000-000000000001","databaseName":"system"}
{"time":"2025-09-22 08:18:46.054+0000","level":"INFO","category":"o.n.k.a.DatabaseAvailabilityGuard","message":"[system/00000000] DatabaseId{00000000[system]} is ready.","databaseId":"00000000-0000-0000-0000-000000000001","databaseName":"system"}
{"time":"2025-09-22 08:18:46.379+0000","level":"INFO","category":"o.n.k.i.a.i.IndexPopulationJob","message":"[system/00000000] Index population started: [Index( id=2, name='index_f7700477', type='LOOKUP', schema=()-[:<any-types>]-(), indexProvider='token-lookup-1.0' )]","databaseId":"00000000-0000-0000-0000-000000000001","databaseName":"system"}
{"time":"2025-09-22 08:18:46.468+0000","level":"INFO","category":"o.n.k.i.a.i.IndexPopulationJob","message":"[system/00000000] Index creation finished for index [Index( id=2, name='index_f7700477', type='LOOKUP', schema=()-[:<any-types>]-(), indexProvider='token-lookup-1.0' )].","databaseId":"00000000-0000-0000-0000-000000000001","databaseName":"system"}
{"time":"2025-09-22 08:18:46.477+0000","level":"INFO","category":"o.n.k.i.a.i.IndexPopulationJob","message":"[system/00000000] Index population started: [Index( id=1, name='index_343aff4e', type='LOOKUP', schema=(:<any-labels>), indexProvider='token-lookup-1.0' )]","databaseId":"00000000-0000-0000-0000-000000000001","databaseName":"system"}
{"time":"2025-09-22 08:18:46.489+0000","level":"INFO","category":"o.n.k.i.a.i.IndexPopulationJob","message":"[system/00000000] Index creation finished for index [Index( id=1, name='index_343aff4e', type='LOOKUP', schema=(:<any-labels>), indexProvider='token-lookup-1.0' )].","databaseId":"00000000-0000-0000-0000-000000000001","databaseName":"system"}
{"time":"2025-09-22 08:18:46.494+0000","level":"INFO","category":"o.n.k.i.t.l.c.CheckPointerImpl","message":"[system/00000000] Checkpoint triggered by \"Database init completed.\" @ txId: 3, append index: 3 checkpoint started...","databaseId":"00000000-0000-0000-0000-000000000001","databaseName":"system"}
{"time":"2025-09-22 08:18:46.593+0000","level":"INFO","category":"o.n.k.i.t.l.c.CheckPointerImpl","message":"[system/00000000] Checkpoint triggered by \"Database init completed.\" @ txId: 3, append index: 3 checkpoint completed in 96ms. Checkpoint flushed 74 pages (0% of total available pages), in 57 IOs. Checkpoint performed with IO limit: unlimited, paused in total 0 times(0 millis). Average checkpoint flush speed: 592.0KiB/s.","databaseId":"00000000-0000-0000-0000-000000000001","databaseName":"system"}
{"time":"2025-09-22 08:18:46.597+0000","level":"INFO","category":"o.n.k.i.t.l.p.LogPruningImpl","message":"[system/00000000] No log version pruned. The strategy used was '2 days ********** size'. ","databaseId":"00000000-0000-0000-0000-000000000001","databaseName":"system"}
{"time":"2025-09-22 08:18:46.607+0000","level":"INFO","category":"o.n.u.UserDataCollector","message":"Anonymous Usage Data is being sent to Neo4j, see https://neo4j.com/docs/usage-data/"}
{"time":"2025-09-22 08:18:46.799+0000","level":"INFO","category":"o.n.k.i.a.i.IndexPopulationJob","message":"[system/00000000] Index population started: [Index( id=3, name='constraint_798238d6', type='RANGE', schema=(:DatabaseName {name, namespace}), indexProvider='range-1.0' )]","databaseId":"00000000-0000-0000-0000-000000000001","databaseName":"system"}
{"time":"2025-09-22 08:18:46.844+0000","level":"INFO","category":"o.n.k.i.a.i.IndexPopulationJob","message":"[system/00000000] Index creation finished for index [Index( id=3, name='constraint_798238d6', type='RANGE', schema=(:DatabaseName {name, namespace}), indexProvider='range-1.0' )].","databaseId":"00000000-0000-0000-0000-000000000001","databaseName":"system"}
{"time":"2025-09-22 08:18:46.873+0000","level":"INFO","category":"o.n.k.i.a.i.IndexingService","message":"[system/00000000] Constraint Index( id=3, name='constraint_798238d6', type='RANGE', schema=(:Label[1] {PropertyKey[11], PropertyKey[12]}), indexProvider='range-1.0' ) is ONLINE.","databaseId":"00000000-0000-0000-0000-000000000001","databaseName":"system"}
{"time":"2025-09-22 08:18:46.888+0000","level":"INFO","category":"o.n.k.i.a.i.IndexPopulationJob","message":"[system/00000000] Index population started: [Index( id=5, name='displayNameConstraint', type='RANGE', schema=(:DatabaseName {displayName}), indexProvider='range-1.0' )]","databaseId":"00000000-0000-0000-0000-000000000001","databaseName":"system"}
{"time":"2025-09-22 08:18:46.901+0000","level":"INFO","category":"o.n.k.i.a.i.IndexPopulationJob","message":"[system/00000000] Index creation finished for index [Index( id=5, name='displayNameConstraint', type='RANGE', schema=(:DatabaseName {displayName}), indexProvider='range-1.0' )].","databaseId":"00000000-0000-0000-0000-000000000001","databaseName":"system"}
{"time":"2025-09-22 08:18:46.911+0000","level":"INFO","category":"o.n.k.i.a.i.IndexingService","message":"[system/00000000] Constraint Index( id=5, name='displayNameConstraint', type='RANGE', schema=(:Label[1] {PropertyKey[17]}), indexProvider='range-1.0' ) is ONLINE.","databaseId":"00000000-0000-0000-0000-000000000001","databaseName":"system"}
{"time":"2025-09-22 08:18:46.935+0000","level":"INFO","category":"o.n.k.i.a.i.IndexPopulationJob","message":"[system/00000000] Index population started: [Index( id=7, name='constraint_8014b60a', type='RANGE', schema=(:Database {name}), indexProvider='range-1.0' )]","databaseId":"00000000-0000-0000-0000-000000000001","databaseName":"system"}
{"time":"2025-09-22 08:18:46.945+0000","level":"INFO","category":"o.n.k.i.a.i.IndexPopulationJob","message":"[system/00000000] Index creation finished for index [Index( id=7, name='constraint_8014b60a', type='RANGE', schema=(:Database {name}), indexProvider='range-1.0' )].","databaseId":"00000000-0000-0000-0000-000000000001","databaseName":"system"}
{"time":"2025-09-22 08:18:46.956+0000","level":"INFO","category":"o.n.k.i.a.i.IndexingService","message":"[system/00000000] Constraint Index( id=7, name='constraint_8014b60a', type='RANGE', schema=(:Label[2] {PropertyKey[11]}), indexProvider='range-1.0' ) is ONLINE.","databaseId":"00000000-0000-0000-0000-000000000001","databaseName":"system"}
{"time":"2025-09-22 08:18:47.177+0000","level":"INFO","category":"o.n.k.i.a.i.IndexPopulationJob","message":"[system/00000000] Index population started: [Index( id=9, name='constraint_5789ae3', type='RANGE', schema=(:User {name}), indexProvider='range-1.0' )]","databaseId":"00000000-0000-0000-0000-000000000001","databaseName":"system"}
{"time":"2025-09-22 08:18:47.194+0000","level":"INFO","category":"o.n.k.i.a.i.IndexPopulationJob","message":"[system/00000000] Index creation finished for index [Index( id=9, name='constraint_5789ae3', type='RANGE', schema=(:User {name}), indexProvider='range-1.0' )].","databaseId":"00000000-0000-0000-0000-000000000001","databaseName":"system"}
{"time":"2025-09-22 08:18:47.202+0000","level":"INFO","category":"o.n.k.i.a.i.IndexingService","message":"[system/00000000] Constraint Index( id=9, name='constraint_5789ae3', type='RANGE', schema=(:Label[3] {PropertyKey[11]}), indexProvider='range-1.0' ) is ONLINE.","databaseId":"00000000-0000-0000-0000-000000000001","databaseName":"system"}
{"time":"2025-09-22 08:18:47.213+0000","level":"INFO","category":"o.n.k.i.a.i.IndexPopulationJob","message":"[system/00000000] Index population started: [Index( id=11, name='constraint_74fad970', type='RANGE', schema=(:User {id}), indexProvider='range-1.0' )]","databaseId":"00000000-0000-0000-0000-000000000001","databaseName":"system"}
{"time":"2025-09-22 08:18:47.235+0000","level":"INFO","category":"o.n.k.i.a.i.IndexPopulationJob","message":"[system/00000000] Index creation finished for index [Index( id=11, name='constraint_74fad970', type='RANGE', schema=(:User {id}), indexProvider='range-1.0' )].","databaseId":"00000000-0000-0000-0000-000000000001","databaseName":"system"}
{"time":"2025-09-22 08:18:47.241+0000","level":"INFO","category":"o.n.k.i.a.i.IndexingService","message":"[system/00000000] Constraint Index( id=11, name='constraint_74fad970', type='RANGE', schema=(:Label[3] {PropertyKey[28]}), indexProvider='range-1.0' ) is ONLINE.","databaseId":"00000000-0000-0000-0000-000000000001","databaseName":"system"}
{"time":"2025-09-22 08:18:47.254+0000","level":"INFO","category":"o.n.k.i.a.i.IndexPopulationJob","message":"[system/00000000] Index population started: [Index( id=13, name='auth-constraint', type='RANGE', schema=(:Auth {provider, id}), indexProvider='range-1.0' )]","databaseId":"00000000-0000-0000-0000-000000000001","databaseName":"system"}
{"time":"2025-09-22 08:18:47.280+0000","level":"INFO","category":"o.n.k.i.a.i.IndexPopulationJob","message":"[system/00000000] Index creation finished for index [Index( id=13, name='auth-constraint', type='RANGE', schema=(:Auth {provider, id}), indexProvider='range-1.0' )].","databaseId":"00000000-0000-0000-0000-000000000001","databaseName":"system"}
{"time":"2025-09-22 08:18:47.286+0000","level":"INFO","category":"o.n.k.i.a.i.IndexingService","message":"[system/00000000] Constraint Index( id=13, name='auth-constraint', type='RANGE', schema=(:Label[4] {PropertyKey[29], PropertyKey[28]}), indexProvider='range-1.0' ) is ONLINE.","databaseId":"00000000-0000-0000-0000-000000000001","databaseName":"system"}
{"time":"2025-09-22 08:18:47.288+0000","level":"INFO","category":"o.n.s.s.s.UserSecurityGraphComponent","message":"Initializing system graph model for component 'security-users' with version -1 and status UNINITIALIZED"}
{"time":"2025-09-22 08:18:47.294+0000","level":"INFO","category":"o.n.s.s.s.UserSecurityGraphComponent","message":"Setting up initial user from defaults: neo4j"}
{"time":"2025-09-22 08:18:47.294+0000","level":"INFO","category":"o.n.s.s.a.CommunitySecurityModule","message":"CREATE USER neo4j PASSWORD ****** CHANGE REQUIRED","message":"CREATE USER neo4j PASSWORD ****** CHANGE REQUIRED","source":"","type":"security"}
{"time":"2025-09-22 08:18:47.312+0000","level":"INFO","category":"o.n.s.s.s.UserSecurityGraphComponent","message":"Setting version for 'security-users' to 5"}
{"time":"2025-09-22 08:18:47.316+0000","level":"INFO","category":"o.n.s.s.s.UserSecurityGraphComponent","message":"After initialization of system graph model component 'security-users' have version 5 and status CURRENT"}
{"time":"2025-09-22 08:18:47.322+0000","level":"INFO","category":"o.n.s.s.s.UserSecurityGraphComponent","message":"Performing postInitialization step for component 'security-users' with version 5 and status CURRENT"}
{"time":"2025-09-22 08:18:47.327+0000","level":"INFO","category":"o.n.d.d.DatabaseLifecycles","message":"Creating 'DatabaseId{03308fa4[neo4j]}'."}
{"time":"2025-09-22 08:18:47.331+0000","level":"INFO","category":"o.n.d.d.DatabaseLifecycles","message":"Starting 'DatabaseId{03308fa4[neo4j]}'."}
{"time":"2025-09-22 08:18:47.346+0000","level":"INFO","category":"o.n.k.d.Database","message":"[neo4j/03308fa4] Current KernelVersion=KernelVersion{V2025_08,version=24}, LogFormat= V9","databaseId":"03308fa4-ae9d-4709-8321-4d70d0e956f8","databaseName":"neo4j"}
{"time":"2025-09-22 08:18:47.348+0000","level":"INFO","category":"o.n.k.i.s.f.RecordFormatSelector","message":"[neo4j/03308fa4] Selected configured format for store E:\\neo4j\\neo4j-community-2025.08.0-windows\\neo4j-community-2025.08.0\\data\\databases\\neo4j: RecordFormat:PageAlignedV5_0[aligned-1.1]","databaseId":"03308fa4-ae9d-4709-8321-4d70d0e956f8","databaseName":"neo4j"}
{"time":"2025-09-22 08:18:47.406+0000","level":"INFO","category":"o.n.i.d.DiagnosticsManager","message":"[neo4j/03308fa4] \r\n                                                                [neo4j/03308fa4] ********************************************************************************\r\n                                                                [neo4j/03308fa4]                               [ Database: neo4j ]                               \r\n                                                                [neo4j/03308fa4] ********************************************************************************\r\n                                                                [neo4j/03308fa4] --------------------------------------------------------------------------------\r\n                                                                [neo4j/03308fa4]                                   [ Version ]                                   \r\n                                                                [neo4j/03308fa4] --------------------------------------------------------------------------------\r\n                                                                [neo4j/03308fa4] DBMS: community record-aligned-1.1\r\n                                                                [neo4j/03308fa4] Kernel version: 2025.08.0\r\n                                                                [neo4j/03308fa4] \r\n                                                                [neo4j/03308fa4] --------------------------------------------------------------------------------\r\n                                                                [neo4j/03308fa4]                                 [ Store files ]                                 \r\n                                                                [neo4j/03308fa4] --------------------------------------------------------------------------------\r\n                                                                [neo4j/03308fa4] Disk space on partition (Total / Free / Free %): 512108785664 / 17338531840 / 3\r\n                                                                [neo4j/03308fa4] Storage files stored on file store: NTFS\r\n                                                                [neo4j/03308fa4] Storage files: (filename : modification date - size)\r\n                                                                [neo4j/03308fa4]   neostore: 2025-09-22 08:18:47.389+0000 - 8.000KiB\r\n                                                                [neo4j/03308fa4]   neostore.counts.db: 2025-09-22 08:18:47.391+0000 - 0B\r\n                                                                [neo4j/03308fa4]   neostore.indexstats.db: 2025-09-22 08:18:47.393+0000 - 0B\r\n                                                                [neo4j/03308fa4]   neostore.labeltokenstore.db: 2025-09-22 08:18:47.382+0000 - 0B\r\n                                                                [neo4j/03308fa4]   neostore.labeltokenstore.db.id: 2025-09-22 08:18:47.380+0000 - 0B\r\n                                                                [neo4j/03308fa4]   neostore.labeltokenstore.db.names: 2025-09-22 08:18:47.379+0000 - 8.000KiB\r\n                                                                [neo4j/03308fa4]   neostore.labeltokenstore.db.names.id: 2025-09-22 08:18:47.378+0000 - 0B\r\n                                                                [neo4j/03308fa4]   neostore.nodestore.db: 2025-09-22 08:18:47.353+0000 - 0B\r\n                                                                [neo4j/03308fa4]   neostore.nodestore.db.id: 2025-09-22 08:18:47.352+0000 - 0B\r\n                                                                [neo4j/03308fa4]   neostore.nodestore.db.labels: 2025-09-22 08:18:47.350+0000 - 8.000KiB\r\n                                                                [neo4j/03308fa4]   neostore.nodestore.db.labels.id: 2025-09-22 08:18:47.349+0000 - 0B\r\n                                                                [neo4j/03308fa4]   neostore.propertystore.db: 2025-09-22 08:18:47.370+0000 - 0B\r\n                                                                [neo4j/03308fa4]   neostore.propertystore.db.arrays: 2025-09-22 08:18:47.364+0000 - 8.000KiB\r\n                                                                [neo4j/03308fa4]   neostore.propertystore.db.arrays.id: 2025-09-22 08:18:47.363+0000 - 0B\r\n                                                                [neo4j/03308fa4]   neostore.propertystore.db.id: 2025-09-22 08:18:47.369+0000 - 0B\r\n                                                                [neo4j/03308fa4]   neostore.propertystore.db.index: 2025-09-22 08:18:47.359+0000 - 0B\r\n                                                                [neo4j/03308fa4]   neostore.propertystore.db.index.id: 2025-09-22 08:18:47.358+0000 - 0B\r\n                                                                [neo4j/03308fa4]   neostore.propertystore.db.index.keys: 2025-09-22 08:18:47.356+0000 - 8.000KiB\r\n                                                                [neo4j/03308fa4]   neostore.propertystore.db.index.keys.id: 2025-09-22 08:18:47.355+0000 - 0B\r\n                                                                [neo4j/03308fa4]   neostore.propertystore.db.strings: 2025-09-22 08:18:47.362+0000 - 8.000KiB\r\n                                                                [neo4j/03308fa4]   neostore.propertystore.db.strings.id: 2025-09-22 08:18:47.360+0000 - 0B\r\n                                                                [neo4j/03308fa4]   neostore.relationshipgroupstore.db: 2025-09-22 08:18:47.387+0000 - 8.000KiB\r\n                                                                [neo4j/03308fa4]   neostore.relationshipgroupstore.db.id: 2025-09-22 08:18:47.386+0000 - 0B\r\n                                                                [neo4j/03308fa4]   neostore.relationshipgroupstore.degrees.db: 2025-09-22 08:18:47.392+0000 - 0B\r\n                                                                [neo4j/03308fa4]   neostore.relationshipstore.db: 2025-09-22 08:18:47.371+0000 - 0B\r\n                                                                [neo4j/03308fa4]   neostore.relationshipstore.db.id: 2025-09-22 08:18:47.371+0000 - 0B\r\n                                                                [neo4j/03308fa4]   neostore.relationshiptypestore.db: 2025-09-22 08:18:47.377+0000 - 0B\r\n                                                                [neo4j/03308fa4]   neostore.relationshiptypestore.db.id: 2025-09-22 08:18:47.377+0000 - 0B\r\n                                                                [neo4j/03308fa4]   neostore.relationshiptypestore.db.names: 2025-09-22 08:18:47.372+0000 - 8.000KiB\r\n                                                                [neo4j/03308fa4]   neostore.relationshiptypestore.db.names.id: 2025-09-22 08:18:47.372+0000 - 0B\r\n                                                                [neo4j/03308fa4]   neostore.schemastore.db: 2025-09-22 08:18:47.384+0000 - 8.000KiB\r\n                                                                [neo4j/03308fa4]   neostore.schemastore.db.id: 2025-09-22 08:18:47.382+0000 - 0B\r\n                                                                [neo4j/03308fa4] Storage summary: \r\n                                                                [neo4j/03308fa4]   Total size of store: 72.00KiB\r\n                                                                [neo4j/03308fa4]   Total size of mapped files: 72.00KiB\r\n                                                                [neo4j/03308fa4] \r\n                                                                [neo4j/03308fa4] --------------------------------------------------------------------------------\r\n                                                                [neo4j/03308fa4]                               [ Transaction log ]                               \r\n                                                                [neo4j/03308fa4] --------------------------------------------------------------------------------\r\n                                                                [neo4j/03308fa4] Transaction log files stored on file store: NTFS\r\n                                                                [neo4j/03308fa4] Transaction log metadata:\r\n                                                                [neo4j/03308fa4]  - current kernel version used in transactions: V2025_08\r\n                                                                [neo4j/03308fa4]  - last committed transaction id: 1\r\n                                                                [neo4j/03308fa4] Transaction log files:\r\n                                                                [neo4j/03308fa4]  - existing transaction log versions: -1--1\r\n                                                                [neo4j/03308fa4]  - no transactions found\r\n                                                                [neo4j/03308fa4] Checkpoint log files:\r\n                                                                [neo4j/03308fa4]  - existing checkpoint log versions: -1--1\r\n                                                                [neo4j/03308fa4]  - no checkpoints found\r\n                                                                [neo4j/03308fa4] \r\n                                                                [neo4j/03308fa4] --------------------------------------------------------------------------------\r\n                                                                [neo4j/03308fa4]                                   [ Id usage ]                                  \r\n                                                                [neo4j/03308fa4] --------------------------------------------------------------------------------\r\n                                                                [neo4j/03308fa4] ArrayPropertyStore[neostore.nodestore.db.labels]: used=1 high=0\r\n                                                                [neo4j/03308fa4] NodeStore[neostore.nodestore.db]: used=0 high=-1\r\n                                                                [neo4j/03308fa4] StringPropertyStore[neostore.propertystore.db.index.keys]: used=1 high=0\r\n                                                                [neo4j/03308fa4] PropertyIndexStore[neostore.propertystore.db.index]: used=0 high=-1\r\n                                                                [neo4j/03308fa4] StringPropertyStore[neostore.propertystore.db.strings]: used=1 high=0\r\n                                                                [neo4j/03308fa4] ArrayPropertyStore[neostore.propertystore.db.arrays]: used=1 high=0\r\n                                                                [neo4j/03308fa4] PropertyStore[neostore.propertystore.db]: used=0 high=-1\r\n                                                                [neo4j/03308fa4] RelationshipStore[neostore.relationshipstore.db]: used=0 high=-1\r\n                                                                [neo4j/03308fa4] StringPropertyStore[neostore.relationshiptypestore.db.names]: used=1 high=0\r\n                                                                [neo4j/03308fa4] RelationshipTypeStore[neostore.relationshiptypestore.db]: used=0 high=-1\r\n                                                                [neo4j/03308fa4] StringPropertyStore[neostore.labeltokenstore.db.names]: used=1 high=0\r\n                                                                [neo4j/03308fa4] LabelTokenStore[neostore.labeltokenstore.db]: used=0 high=-1\r\n                                                                [neo4j/03308fa4] SchemaStore[neostore.schemastore.db]: used=1 high=0\r\n                                                                [neo4j/03308fa4] RelationshipGroupStore[neostore.relationshipgroupstore.db]: used=1 high=0\r\n                                                                [neo4j/03308fa4] NeoStore[neostore]: used=-1 high=-1\r\n                                                                [neo4j/03308fa4] \r\n                                                                [neo4j/03308fa4] --------------------------------------------------------------------------------\r\n                                                                [neo4j/03308fa4]                                   [ Metadata ]                                  \r\n                                                                [neo4j/03308fa4] --------------------------------------------------------------------------------\r\n                                                                [neo4j/03308fa4] EXTERNAL_STORE_UUID (Database identifier exposed as external store identity. Generated on creation and never updated): 9aa8d521-762b-4e40-9d80-9bc7d39e9d8e\r\n                                                                [neo4j/03308fa4] DATABASE_ID (The last used DatabaseId for this database): null\r\n                                                                [neo4j/03308fa4] LEGACY_STORE_VERSION (Legacy store format version. This field is used from 5.0 onwards only to distinguish non-migrated pre 5.0 metadata stores.): -3523014627327384477\r\n                                                                [neo4j/03308fa4] STORE_ID (Store ID): StoreId{creationTime=1758529127389, random=2976458135536663148, storageEngineName='record', formatName='aligned', majorVersion=1, minorVersion=1}\r\n                                                                [neo4j/03308fa4] "}
{"time":"2025-09-22 08:18:47.408+0000","level":"INFO","category":"o.n.k.a.DatabaseAvailabilityGuard","message":"[neo4j/03308fa4] Requirement `Database unavailable` makes database neo4j unavailable.","databaseId":"03308fa4-ae9d-4709-8321-4d70d0e956f8","databaseName":"neo4j"}
{"time":"2025-09-22 08:18:47.409+0000","level":"INFO","category":"o.n.k.a.DatabaseAvailabilityGuard","message":"[neo4j/03308fa4] DatabaseId{03308fa4[neo4j]} is unavailable.","databaseId":"03308fa4-ae9d-4709-8321-4d70d0e956f8","databaseName":"neo4j"}
{"time":"2025-09-22 08:18:47.411+0000","level":"WARN","category":"o.n.k.i.s.MetaDataStore","message":"[neo4j/03308fa4] Missing counts store, rebuilding it.","databaseId":"03308fa4-ae9d-4709-8321-4d70d0e956f8","databaseName":"neo4j"}
{"time":"2025-09-22 08:18:47.413+0000","level":"WARN","category":"o.n.k.i.s.MetaDataStore","message":"[neo4j/03308fa4] Counts store rebuild completed.","databaseId":"03308fa4-ae9d-4709-8321-4d70d0e956f8","databaseName":"neo4j"}
{"time":"2025-09-22 08:18:47.416+0000","level":"INFO","category":"o.n.k.d.Database","message":"[neo4j/03308fa4] Starting transaction log [E:\\neo4j\\neo4j-community-2025.08.0-windows\\neo4j-community-2025.08.0\\data\\transactions\\neo4j\\neostore.transaction.db.0] at version=0","databaseId":"03308fa4-ae9d-4709-8321-4d70d0e956f8","databaseName":"neo4j"}
{"time":"2025-09-22 08:18:47.421+0000","level":"INFO","category":"o.n.k.d.Database","message":"[neo4j/03308fa4] Starting transaction log [E:\\neo4j\\neo4j-community-2025.08.0-windows\\neo4j-community-2025.08.0\\data\\transactions\\neo4j\\checkpoint.0] at version=0","databaseId":"03308fa4-ae9d-4709-8321-4d70d0e956f8","databaseName":"neo4j"}
{"time":"2025-09-22 08:18:47.423+0000","level":"INFO","category":"o.n.k.i.t.l.f.c.CheckpointLogFile","message":"[neo4j/03308fa4] Scanning log file with version 0 for checkpoint entries","databaseId":"03308fa4-ae9d-4709-8321-4d70d0e956f8","databaseName":"neo4j"}
{"time":"2025-09-22 08:18:47.427+0000","level":"INFO","category":"o.n.k.a.DatabaseAvailabilityGuard","message":"[neo4j/03308fa4] Fulfilling of requirement 'Database unavailable' makes database neo4j available.","databaseId":"03308fa4-ae9d-4709-8321-4d70d0e956f8","databaseName":"neo4j"}
{"time":"2025-09-22 08:18:47.427+0000","level":"INFO","category":"o.n.k.a.DatabaseAvailabilityGuard","message":"[neo4j/03308fa4] DatabaseId{03308fa4[neo4j]} is ready.","databaseId":"03308fa4-ae9d-4709-8321-4d70d0e956f8","databaseName":"neo4j"}
{"time":"2025-09-22 08:18:47.431+0000","level":"INFO","category":"o.n.k.i.a.i.IndexPopulationJob","message":"[neo4j/03308fa4] Index population started: [Index( id=2, name='index_f7700477', type='LOOKUP', schema=()-[:<any-types>]-(), indexProvider='token-lookup-1.0' )]","databaseId":"03308fa4-ae9d-4709-8321-4d70d0e956f8","databaseName":"neo4j"}
{"time":"2025-09-22 08:18:47.459+0000","level":"INFO","category":"o.n.k.i.a.i.IndexPopulationJob","message":"[neo4j/03308fa4] Index creation finished for index [Index( id=2, name='index_f7700477', type='LOOKUP', schema=()-[:<any-types>]-(), indexProvider='token-lookup-1.0' )].","databaseId":"03308fa4-ae9d-4709-8321-4d70d0e956f8","databaseName":"neo4j"}
{"time":"2025-09-22 08:18:47.464+0000","level":"INFO","category":"o.n.k.i.a.i.IndexPopulationJob","message":"[neo4j/03308fa4] Index population started: [Index( id=1, name='index_343aff4e', type='LOOKUP', schema=(:<any-labels>), indexProvider='token-lookup-1.0' )]","databaseId":"03308fa4-ae9d-4709-8321-4d70d0e956f8","databaseName":"neo4j"}
{"time":"2025-09-22 08:18:47.480+0000","level":"INFO","category":"o.n.k.i.a.i.IndexPopulationJob","message":"[neo4j/03308fa4] Index creation finished for index [Index( id=1, name='index_343aff4e', type='LOOKUP', schema=(:<any-labels>), indexProvider='token-lookup-1.0' )].","databaseId":"03308fa4-ae9d-4709-8321-4d70d0e956f8","databaseName":"neo4j"}
{"time":"2025-09-22 08:18:47.483+0000","level":"INFO","category":"o.n.k.i.t.l.c.CheckPointerImpl","message":"[neo4j/03308fa4] Checkpoint triggered by \"Database init completed.\" @ txId: 3, append index: 3 checkpoint started...","databaseId":"03308fa4-ae9d-4709-8321-4d70d0e956f8","databaseName":"neo4j"}
{"time":"2025-09-22 08:18:47.571+0000","level":"INFO","category":"o.n.k.i.t.l.c.CheckPointerImpl","message":"[neo4j/03308fa4] Checkpoint triggered by \"Database init completed.\" @ txId: 3, append index: 3 checkpoint completed in 87ms. Checkpoint flushed 74 pages (0% of total available pages), in 57 IOs. Checkpoint performed with IO limit: unlimited, paused in total 0 times(0 millis). Average checkpoint flush speed: 592.0KiB/s.","databaseId":"03308fa4-ae9d-4709-8321-4d70d0e956f8","databaseName":"neo4j"}
{"time":"2025-09-22 08:18:47.572+0000","level":"INFO","category":"o.n.k.i.t.l.p.LogPruningImpl","message":"[neo4j/03308fa4] No log version pruned. The strategy used was '2 days ********** size'. ","databaseId":"03308fa4-ae9d-4709-8321-4d70d0e956f8","databaseName":"neo4j"}
{"time":"2025-09-22 08:18:47.676+0000","level":"INFO","category":"o.n.b.p.c.c.n.SocketNettyConnector","message":"Bolt enabled on localhost:7687."}
{"time":"2025-09-22 08:18:47.681+0000","level":"WARN","category":"i.n.b.ServerBootstrap","message":"Unknown channel option 'SO_REUSEADDR' for channel '[id: 0x326e1949]'"}
{"time":"2025-09-22 08:18:47.692+0000","level":"INFO","category":"o.n.b.BoltServer","message":"Bolt server started"}
{"time":"2025-09-22 08:18:47.693+0000","level":"INFO","category":"o.n.s.A.ServerComponentsLifecycleAdapter","message":"Starting web server"}
{"time":"2025-09-22 08:18:48.903+0000","level":"INFO","category":"o.n.s.CommunityNeoWebServer","message":"HTTP enabled on localhost:7474."}
{"time":"2025-09-22 08:18:48.904+0000","level":"INFO","category":"o.n.s.CommunityNeoWebServer","message":"Remote interface available at http://localhost:7474/"}
{"time":"2025-09-22 08:18:48.904+0000","level":"INFO","category":"o.n.s.A.ServerComponentsLifecycleAdapter","message":"Web server started."}
{"time":"2025-09-22 08:18:48.908+0000","level":"INFO","category":"o.n.g.f.DatabaseManagementServiceFactory","message":"id: 0218C4F89E5A67861172F8AC28DC672FE65E55995A506B20293349DA824C4F54"}
{"time":"2025-09-22 08:18:48.908+0000","level":"INFO","category":"o.n.g.f.DatabaseManagementServiceFactory","message":"name: system"}
{"time":"2025-09-22 08:18:48.909+0000","level":"INFO","category":"o.n.g.f.DatabaseManagementServiceFactory","message":"creationDate: 2025-09-22T08:18:45.653Z"}
{"time":"2025-09-22 08:20:16.427+0000","level":"INFO","category":"o.n.g.f.m.GlobalModule","message":"Logging config in use: File 'E:\\neo4j\\neo4j-community-2025.08.0-windows\\neo4j-community-2025.08.0\\conf\\server-logs.xml'"}
{"time":"2025-09-22 08:20:16.627+0000","level":"WARN","category":"o.n.k.i.JvmChecker","message":"The max heap memory has not been configured. It is recommended that it is always explicitly configured, to ensure the system has a balanced configuration. Until then, a JVM computed heuristic of 4261412864 bytes is used instead. If you are running neo4j server, you need to configure server.memory.heap.max_size in neo4j.conf. If you are running neo4j embedded, you have to launch the JVM with -Xmx set to a value. You can run neo4j-admin server memory-recommendation for memory configuration suggestions."}
{"time":"2025-09-22 08:20:16.627+0000","level":"WARN","category":"o.n.k.i.JvmChecker","message":"The initial heap memory has not been configured. It is recommended that it is always explicitly configured, to ensure the system has a balanced configuration. Until then, a JVM computed heuristic of 266338304 bytes is used instead. If you are running neo4j server, you need to configure server.memory.heap.initial_size in neo4j.conf. If you are running neo4j embedded, you have to launch the JVM with -Xms set to a value. You can run neo4j-admin server memory-recommendation for memory configuration suggestions."}
{"time":"2025-09-22 08:20:16.904+0000","level":"WARN","category":"o.n.i.p.PageCache","message":"The server.memory.pagecache.size setting has not been configured. It is recommended that this setting is always explicitly configured, to ensure the system has a balanced configuration. Until then, a computed heuristic value of 6390712320 bytes will be used instead. Run `neo4j-admin memory-recommendation` for memory configuration suggestions."}
{"time":"2025-09-22 08:20:17.097+0000","level":"INFO","category":"o.n.d.i.DefaultIdentityModule","message":"Found ServerId on disk: ServerId{b35704b1} (b35704b1-c3a8-499a-ba0c-4ffa7fac8f09)"}
{"time":"2025-09-22 08:20:17.099+0000","level":"INFO","category":"o.n.d.i.DefaultIdentityModule","message":"This instance is ServerId{b35704b1} (b35704b1-c3a8-499a-ba0c-4ffa7fac8f09)"}
{"time":"2025-09-22 08:20:19.484+0000","level":"INFO","category":"o.n.s.CommunityNeoWebServer","message":"======== Neo4j 2025.08.0 ========"}
{"time":"2025-09-22 08:20:19.835+0000","level":"INFO","category":"o.n.d.d.DatabaseLifecycles","message":"Creating 'DatabaseId{00000000[system]}'."}
{"time":"2025-09-22 08:20:19.836+0000","level":"INFO","category":"o.n.i.d.DiagnosticsManager","message":"\r\n                                                                ********************************************************************************\r\n                                                                                             [ System diagnostics ]                             \r\n                                                                ********************************************************************************"}
{"time":"2025-09-22 08:20:19.836+0000","level":"INFO","category":"o.n.i.d.DiagnosticsManager","message":"\r\n                                                                --------------------------------------------------------------------------------\r\n                                                                                         [ System memory information ]                          \r\n                                                                --------------------------------------------------------------------------------\r\n                                                                Total Physical memory: 15.87GiB\r\n                                                                Free Physical memory: 835.4MiB\r\n                                                                Committed virtual memory: 324.6MiB\r\n                                                                Total swap space: 30.87GiB\r\n                                                                Free swap space: 7.701GiB\r\n                                                                "}
{"time":"2025-09-22 08:20:19.836+0000","level":"INFO","category":"o.n.i.d.DiagnosticsManager","message":"\r\n                                                                --------------------------------------------------------------------------------\r\n                                                                                           [ JVM memory information ]                           \r\n                                                                --------------------------------------------------------------------------------\r\n                                                                Free  memory: 64.20MiB\r\n                                                                Total memory: 100.0MiB\r\n                                                                Max   memory: 3.969GiB\r\n                                                                Garbage Collector: G1 Young Generation: [G1 Eden Space, G1 Survivor Space, G1 Old Gen]\r\n                                                                Garbage Collector: G1 Concurrent GC: [G1 Old Gen]\r\n                                                                Garbage Collector: G1 Old Generation: [G1 Eden Space, G1 Survivor Space, G1 Old Gen]\r\n                                                                Memory Pool: CodeHeap 'non-nmethods' (Non-heap memory): committed=3.313MiB, used=3.266MiB, max=7.313MiB, threshold=0B\r\n                                                                Memory Pool: Metaspace (Non-heap memory): committed=49.06MiB, used=48.40MiB, max=-1B, threshold=0B\r\n                                                                Memory Pool: CodeHeap 'profiled nmethods' (Non-heap memory): committed=8.750MiB, used=8.547MiB, max=116.3MiB, threshold=0B\r\n                                                                Memory Pool: Compressed Class Space (Non-heap memory): committed=6.188MiB, used=5.918MiB, max=1.000GiB, threshold=0B\r\n                                                                Memory Pool: G1 Eden Space (Heap memory): committed=56.00MiB, used=2.000MiB, max=-1B, threshold=?\r\n                                                                Memory Pool: G1 Old Gen (Heap memory): committed=38.00MiB, used=27.91MiB, max=3.969GiB, threshold=0B\r\n                                                                Memory Pool: G1 Survivor Space (Heap memory): committed=6.000MiB, used=5.480MiB, max=-1B, threshold=?\r\n                                                                Memory Pool: CodeHeap 'non-profiled nmethods' (Non-heap memory): committed=2.563MiB, used=2.550MiB, max=116.4MiB, threshold=0B\r\n                                                                "}
{"time":"2025-09-22 08:20:19.837+0000","level":"INFO","category":"o.n.i.d.DiagnosticsManager","message":"\r\n                                                                --------------------------------------------------------------------------------\r\n                                                                                        [ Operating system information ]                        \r\n                                                                --------------------------------------------------------------------------------\r\n                                                                Operating System: Windows 10; version: 10.0; arch: amd64; cpus: 16\r\n                                                                Process id: 16156\r\n                                                                Byte order: LITTLE_ENDIAN\r\n                                                                Local timezone: Asia/Shanghai\r\n                                                                Memory page size: 4096\r\n                                                                Unaligned memory access allowed: true\r\n                                                                "}
{"time":"2025-09-22 08:20:19.837+0000","level":"INFO","category":"o.n.i.d.DiagnosticsManager","message":"\r\n                                                                --------------------------------------------------------------------------------\r\n                                                                                              [ JVM information ]                               \r\n                                                                --------------------------------------------------------------------------------\r\n                                                                VM Name: OpenJDK 64-Bit Server VM\r\n                                                                VM Vendor: Eclipse Adoptium\r\n                                                                VM Version: 21.0.8+9-LTS\r\n                                                                JIT compiler: HotSpot 64-Bit Tiered Compilers\r\n                                                                VM Arguments: [-XX:+UseG1GC, -XX:-OmitStackTraceInFastThrow, -XX:+AlwaysPreTouch, -XX:+UnlockExperimentalVMOptions, -XX:+TrustFinalNonStaticFields, -XX:+DisableExplicitGC, -Djdk.nio.maxCachedBufferSize=1024, -Dio.netty.tryReflectionSetAccessible=true, -Dio.netty.leakDetection.level=DISABLED, -Djdk.tls.ephemeralDHKeySize=2048, -Djdk.tls.rejectClientInitiatedRenegotiation=true, -XX:FlightRecorderOptions=stackdepth=256, -XX:+UnlockDiagnosticVMOptions, -XX:+DebugNonSafepoints, --add-opens=java.base/java.nio=ALL-UNNAMED, --add-opens=java.base/java.io=ALL-UNNAMED, --add-opens=java.base/sun.nio.ch=ALL-UNNAMED, --add-opens=java.base/java.util.concurrent=ALL-UNNAMED, --enable-native-access=ALL-UNNAMED, -Dlog4j2.disable.jmx=true, -Dlog4j.layout.jsonTemplate.maxStringLength=32768, -Dfile.encoding=UTF-8]\r\n                                                                "}
{"time":"2025-09-22 08:20:19.837+0000","level":"INFO","category":"o.n.i.d.DiagnosticsManager","message":"\r\n\r\n                                                                --------------------------------------------------------------------------------\r\n                                                                                               [ Java classpath ]                               \r\n                                                                --------------------------------------------------------------------------------\r\n                                                                 [classpath] E:\\neo4j\\neo4j-community-2025.08.0-windows\\neo4j-community-2025.08.0\\lib\\scala-reflect-2.13.16.jar\r\n                                                                 [classpath] E:\\neo4j\\neo4j-community-2025.08.0-windows\\neo4j-community-2025.08.0\\lib\\neo4j-consistency-check-2025.08.0.jar\r\n                                                                 [classpath] E:\\neo4j\\neo4j-community-2025.08.0-windows\\neo4j-community-2025.08.0\\lib\\listenablefuture-9999.0-empty-to-avoid-conflict-with-guava.jar\r\n                                                                 [classpath] E:\\neo4j\\neo4j-community-2025.08.0-windows\\neo4j-community-2025.08.0\\lib\\neo4j-java-driver-6.0.0-alpha03.jar\r\n                                                                 [classpath] E:\\neo4j\\neo4j-community-2025.08.0-windows\\neo4j-community-2025.08.0\\lib\\jctools-core-4.0.5.jar\r\n                                                                 [classpath] E:\\neo4j\\neo4j-community-2025.08.0-windows\\neo4j-community-2025.08.0\\lib\\guava-33.4.8-jre.jar\r\n                                                                 [classpath] E:\\neo4j\\neo4j-community-2025.08.0-windows\\neo4j-community-2025.08.0\\lib\\neo4j-cypher-cache-2025.08.0.jar\r\n                                                                 [classpath] E:\\neo4j\\neo4j-community-2025.08.0-windows\\neo4j-community-2025.08.0\\lib\\grpc-protobuf-1.73.0.jar\r\n                                                                 [classpath] E:\\neo4j\\neo4j-community-2025.08.0-windows\\neo4j-community-2025.08.0\\lib\\neo4j-diagnostics-2025.08.0.jar\r\n                                                                 [classpath] E:\\neo4j\\neo4j-community-2025.08.0-windows\\neo4j-community-2025.08.0\\lib\\neo4j-expressions-2025.08.0.jar\r\n                                                                 [classpath] E:\\neo4j\\neo4j-community-2025.08.0-windows\\neo4j-community-2025.08.0\\lib\\neo4j-collections-2025.08.0.jar\r\n                                                                 [classpath] E:\\neo4j\\neo4j-community-2025.08.0-windows\\neo4j-community-2025.08.0\\lib\\neo4j-ast-2025.08.0.jar\r\n                                                                 [classpath] E:\\neo4j\\neo4j-community-2025.08.0-windows\\neo4j-community-2025.08.0\\lib\\cypher-parser-ast-common-2025.08.0.jar\r\n                                                                 [classpath] E:\\neo4j\\neo4j-community-2025.08.0-windows\\neo4j-community-2025.08.0\\lib\\stax-ex-1.8.1.jar\r\n                                                                 [classpath] E:\\neo4j\\neo4j-community-2025.08.0-windows\\neo4j-community-2025.08.0\\lib\\jsr305-3.0.2.jar\r\n                                                                 [classpath] E:\\neo4j\\neo4j-community-2025.08.0-windows\\neo4j-community-2025.08.0\\lib\\neo4j-codegen-2025.08.0.jar\r\n                                                                 [classpath] E:\\neo4j\\neo4j-community-2025.08.0-windows\\neo4j-community-2025.08.0\\lib\\neo4j-exceptions-2025.08.0.jar\r\n                                                                 [classpath] E:\\neo4j\\neo4j-community-2025.08.0-windows\\neo4j-community-2025.08.0\\lib\\neo4j-capabilities-2025.08.0.jar\r\n                                                                 [classpath] E:\\neo4j\\neo4j-community-2025.08.0-windows\\neo4j-community-2025.08.0\\lib\\jetty-http2-hpack-12.0.17.jar\r\n                                                                 [classpath] E:\\neo4j\\neo4j-community-2025.08.0-windows\\neo4j-community-2025.08.0\\lib\\neo4j-data-collector-2025.08.0.jar\r\n                                                                 [classpath] E:\\neo4j\\neo4j-community-2025.08.0-windows\\neo4j-community-2025.08.0\\lib\\reactive-streams-1.0.4.jar\r\n                                                                 [classpath] E:\\neo4j\\neo4j-community-2025.08.0-windows\\neo4j-community-2025.08.0\\lib\\jaxb-api-2.2.12.jar\r\n                                                                 [classpath] E:\\neo4j\\neo4j-community-2025.08.0-windows\\neo4j-community-2025.08.0\\lib\\arrow-vector-18.3.0.jar\r\n                                                                 [classpath] E:\\neo4j\\neo4j-community-2025.08.0-windows\\neo4j-community-2025.08.0\\lib\\jetty-ee8-nested-12.0.17.jar\r\n                                                                 [classpath] E:\\neo4j\\neo4j-community-2025.08.0-windows\\neo4j-community-2025.08.0\\lib\\jakarta.activation-api-1.2.2.jar\r\n                                                                 [classpath] E:\\neo4j\\neo4j-community-2025.08.0-windows\\neo4j-community-2025.08.0\\lib\\jansi-2.4.0.jar\r\n                                                                 [classpath] E:\\neo4j\\neo4j-community-2025.08.0-windows\\neo4j-community-2025.08.0\\lib\\neo4j-util-2025.08.0.jar\r\n                                                                 [classpath] E:\\neo4j\\neo4j-community-2025.08.0-windows\\neo4j-community-2025.08.0\\lib\\neo4j-fabric-2025.08.0.jar\r\n                                                                 [classpath] E:\\neo4j\\neo4j-community-2025.08.0-windows\\neo4j-community-2025.08.0\\lib\\jakarta.annotation-api-1.3.5.jar\r\n                                                                 [classpath] E:\\neo4j\\neo4j-community-2025.08.0-windows\\neo4j-community-2025.08.0\\lib\\parquet-encoding-1.15.2.jar\r\n                                                                 [classpath] E:\\neo4j\\neo4j-community-2025.08.0-windows\\neo4j-community-2025.08.0\\lib\\jna-5.17.0.jar\r\n                                                                 [classpath] E:\\neo4j\\neo4j-community-2025.08.0-windows\\neo4j-community-2025.08.0\\lib\\jersey-server-2.43.jar\r\n                                                                 [classpath] E:\\neo4j\\neo4j-community-2025.08.0-windows\\neo4j-community-2025.08.0\\lib\\jetty-http2-server-12.0.17.jar\r\n                                                                 [classpath] E:\\neo4j\\neo4j-community-2025.08.0-windows\\neo4j-community-2025.08.0\\lib\\txw2-2.3.2.jar\r\n                                                                 [classpath] E:\\neo4j\\neo4j-community-2025.08.0-windows\\neo4j-community-2025.08.0\\lib\\shiro-lang-2.0.5.jar\r\n                                                                 [classpath] E:\\neo4j\\neo4j-community-2025.08.0-windows\\neo4j-community-2025.08.0\\lib\\neo4j-layout-2025.08.0.jar\r\n                                                                 [classpath] E:\\neo4j\\neo4j-community-2025.08.0-windows\\neo4j-community-2025.08.0\\lib\\log4j-api-2.20.0.jar\r\n                                                                 [classpath] E:\\neo4j\\neo4j-community-2025.08.0-windows\\neo4j-community-2025.08.0\\lib\\jackson-jaxrs-base-2.19.1.jar\r\n                                                                 [classpath] E:\\neo4j\\neo4j-community-2025.08.0-windows\\neo4j-community-2025.08.0\\lib\\jakarta.validation-api-2.0.2.jar\r\n                                                                 [classpath] E:\\neo4j\\neo4j-community-2025.08.0-windows\\neo4j-community-2025.08.0\\lib\\grpc-netty-1.73.0.jar\r\n                                                                 [classpath] E:\\neo4j\\neo4j-community-2025.08.0-windows\\neo4j-community-2025.08.0\\lib\\flight-core-18.3.0.jar\r\n                                                                 [classpath] E:\\neo4j\\neo4j-community-2025.08.0-windows\\neo4j-community-2025.08.0\\lib\\neo4j-native-2025.08.0.jar\r\n                                                                 [classpath] E:\\neo4j\\neo4j-community-2025.08.0-windows\\neo4j-community-2025.08.0\\lib\\commons-configuration2-2.12.0.jar\r\n                                                                 [classpath] E:\\neo4j\\neo4j-community-2025.08.0-windows\\neo4j-community-2025.08.0\\lib\\neo4j-record-storage-engine-2025.08.0.jar\r\n                                                                 [classpath] E:\\neo4j\\neo4j-community-2025.08.0-windows\\neo4j-community-2025.08.0\\lib\\neo4j-bolt-2025.08.0.jar\r\n                                                                 [classpath] E:\\neo4j\\neo4j-community-2025.08.0-windows\\neo4j-community-2025.08.0\\lib\\neo4j-2025.08.0.jar"}
{"time":"2025-09-22 08:20:19.840+0000","level":"INFO","category":"o.n.i.d.DiagnosticsManager","message":"\r\n                                                                 [classpath] E:\\neo4j\\neo4j-community-2025.08.0-windows\\neo4j-community-2025.08.0\\lib\\zstd-jni-1.5.7-4.jar\r\n                                                                 [classpath] E:\\neo4j\\neo4j-community-2025.08.0-windows\\neo4j-community-2025.08.0\\lib\\neo4j-kernel-2025.08.0.jar\r\n                                                                 [classpath] E:\\neo4j\\neo4j-community-2025.08.0-windows\\neo4j-community-2025.08.0\\lib\\asm-util-9.8.jar\r\n                                                                 [classpath] E:\\neo4j\\neo4j-community-2025.08.0-windows\\neo4j-community-2025.08.0\\lib\\neo4j-lucene-index-2025.08.0.jar\r\n                                                                 [classpath] E:\\neo4j\\neo4j-community-2025.08.0-windows\\neo4j-community-2025.08.0\\lib\\neo4j-bolt-connection-routed-6.0.2.jar\r\n                                                                 [classpath] E:\\neo4j\\neo4j-community-2025.08.0-windows\\neo4j-community-2025.08.0\\lib\\jPowerShell-3.0.jar\r\n                                                                 [classpath] E:\\neo4j\\neo4j-community-2025.08.0-windows\\neo4j-community-2025.08.0\\lib\\magnolia_2.13-1.1.8.jar\r\n                                                                 [classpath] E:\\neo4j\\neo4j-community-2025.08.0-windows\\neo4j-community-2025.08.0\\lib\\antlr4-runtime-4.13.2.jar\r\n                                                                 [classpath] E:\\neo4j\\neo4j-community-2025.08.0-windows\\neo4j-community-2025.08.0\\lib\\jline-terminal-jansi-3.21.0.jar\r\n                                                                 [classpath] E:\\neo4j\\neo4j-community-2025.08.0-windows\\neo4j-community-2025.08.0\\lib\\arrow-memory-netty-buffer-patch-18.3.0.jar\r\n                                                                 [classpath] E:\\neo4j\\neo4j-community-2025.08.0-windows\\neo4j-community-2025.08.0\\lib\\jersey-client-2.43.jar\r\n                                                                 [classpath] E:\\neo4j\\neo4j-community-2025.08.0-windows\\neo4j-community-2025.08.0\\lib\\neo4j-unsafe-2025.08.0.jar\r\n                                                                 [classpath] E:\\neo4j\\neo4j-community-2025.08.0-windows\\neo4j-community-2025.08.0\\lib\\neo4j-cypher-dsl-2024.7.1.jar\r\n                                                                 [classpath] E:\\neo4j\\neo4j-community-2025.08.0-windows\\neo4j-community-2025.08.0\\lib\\asm-tree-9.8.jar\r\n                                                                 [classpath] E:\\neo4j\\neo4j-community-2025.08.0-windows\\neo4j-community-2025.08.0\\lib\\netty-transport-native-epoll-4.2.4.Final-linux-aarch_64.jar\r\n                                                                 [classpath] E:\\neo4j\\neo4j-community-2025.08.0-windows\\neo4j-community-2025.08.0\\lib\\parquet-hadoop-1.15.2.jar\r\n                                                                 [classpath] E:\\neo4j\\neo4j-community-2025.08.0-windows\\neo4j-community-2025.08.0\\lib\\shiro-event-2.0.5.jar\r\n                                                                 [classpath] E:\\neo4j\\neo4j-community-2025.08.0-windows\\neo4j-community-2025.08.0\\lib\\neo4j-cypher-slotted-runtime-2025.08.0.jar\r\n                                                                 [classpath] E:\\neo4j\\neo4j-community-2025.08.0-windows\\neo4j-community-2025.08.0\\lib\\neo4j-cypher-planner-2025.08.0.jar\r\n                                                                 [classpath] E:\\neo4j\\neo4j-community-2025.08.0-windows\\neo4j-community-2025.08.0\\lib\\cypher-v5-ast-factory-2025.08.0.jar\r\n                                                                 [classpath] E:\\neo4j\\neo4j-community-2025.08.0-windows\\neo4j-community-2025.08.0\\lib\\cypher-v25-parser-listener-2025.08.0.jar\r\n                                                                 [classpath] E:\\neo4j\\neo4j-community-2025.08.0-windows\\neo4j-community-2025.08.0\\lib\\parquet-floor-1.57.jar\r\n                                                                 [classpath] E:\\neo4j\\neo4j-community-2025.08.0-windows\\neo4j-community-2025.08.0\\lib\\neo4j-dbms-2025.08.0.jar\r\n                                                                 [classpath] E:\\neo4j\\neo4j-community-2025.08.0-windows\\neo4j-community-2025.08.0\\lib\\jersey-container-servlet-2.43.jar\r\n                                                                 [classpath] E:\\neo4j\\neo4j-community-2025.08.0-windows\\neo4j-community-2025.08.0\\lib\\jackson-databind-2.19.1.jar\r\n                                                                 [classpath] E:\\neo4j\\neo4j-community-2025.08.0-windows\\neo4j-community-2025.08.0\\lib\\parquet-jackson-1.15.2.jar\r\n                                                                 [classpath] E:\\neo4j\\neo4j-community-2025.08.0-windows\\neo4j-community-2025.08.0\\lib\\jetty-ee8-webapp-12.0.17.jar\r\n                                                                 [classpath] E:\\neo4j\\neo4j-community-2025.08.0-windows\\neo4j-community-2025.08.0\\lib\\parquet-column-1.15.2.jar\r\n                                                                 [classpath] E:\\neo4j\\neo4j-community-2025.08.0-windows\\neo4j-community-2025.08.0\\lib\\neo4j-cypher-config-2025.08.0.jar\r\n                                                                 [classpath] E:\\neo4j\\neo4j-community-2025.08.0-windows\\neo4j-community-2025.08.0\\lib\\netty-transport-native-kqueue-4.2.4.Final-osx-aarch_64.jar\r\n                                                                 [classpath] E:\\neo4j\\neo4j-community-2025.08.0-windows\\neo4j-community-2025.08.0\\lib\\cypher-v25-ast-factory-2025.08.0.jar\r\n                                                                 [classpath] E:\\neo4j\\neo4j-community-2025.08.0-windows\\neo4j-community-2025.08.0\\lib\\neo4j-procedure-api-2025.08.0.jar\r\n                                                                 [classpath] E:\\neo4j\\neo4j-community-2025.08.0-windows\\neo4j-community-2025.08.0\\lib\\netty-codec-http2-4.2.4.Final.jar\r\n                                                                 [classpath] E:\\neo4j\\neo4j-community-2025.08.0-windows\\neo4j-community-2025.08.0\\conf\\*\r\n                                                                 [classpath] E:\\neo4j\\neo4j-community-2025.08.0-windows\\neo4j-community-2025.08.0\\lib\\arrow-format-18.3.0.jar\r\n                                                                 [classpath] E:\\neo4j\\neo4j-community-2025.08.0-windows\\neo4j-community-2025.08.0\\lib\\neo4j-token-api-2025.08.0.jar\r\n                                                                 [classpath] E:\\neo4j\\neo4j-community-2025.08.0-windows\\neo4j-community-2025.08.0\\lib\\neo4j-command-line-2025.08.0.jar\r\n                                                                 [classpath] E:\\neo4j\\neo4j-community-2025.08.0-windows\\neo4j-community-2025.08.0\\lib\\cypher-antlr-common-2025.08.0.jar\r\n                                                                 [classpath] E:\\neo4j\\neo4j-community-2025.08.0-windows\\neo4j-community-2025.08.0\\lib\\commons-logging-1.3.5.jar\r\n                                                                 [classpath] E:\\neo4j\\neo4j-community-2025.08.0-windows\\neo4j-community-2025.08.0\\lib\\neo4j-front-end-2025.08.0.jar\r\n                                                                 [classpath] E:\\neo4j\\neo4j-community-2025.08.0-windows\\neo4j-community-2025.08.0\\lib\\cypher-parser-common-2025.08.0.jar\r\n                                                                 [classpath] E:\\neo4j\\neo4j-community-2025.08.0-windows\\neo4j-community-2025.08.0\\lib\\neo4j-udc-2025.08.0.jar\r\n                                                                 [classpath] E:\\neo4j\\neo4j-community-2025.08.0-windows\\neo4j-community-2025.08.0\\lib\\grpc-util-1.73.0.jar\r\n                                                                 [classpath] E:\\neo4j\\neo4j-community-2025.08.0-windows\\neo4j-community-2025.08.0\\lib\\neo4j-cypher-planner-spi-2025.08.0.jar\r\n                                                                 [classpath] E:\\neo4j\\neo4j-community-2025.08.0-windows\\neo4j-community-2025.08.0\\lib\\jersey-hk2-2.43.jar\r\n                                                                 [classpath] E:\\neo4j\\neo4j-community-2025.08.0-windows\\neo4j-community-2025.08.0\\lib\\shiro-crypto-cipher-2.0.5.jar\r\n                                                                 [classpath] E:\\neo4j\\neo4j-community-2025.08.0-windows\\neo4j-community-2025.08.0\\lib\\jackson-datatype-jsr310-2.18.3.jar\r\n                                                                 [classpath] E:\\neo4j\\neo4j-community-2025.08.0-windows\\neo4j-community-2025.08.0\\lib\\neo4j-cypher-2025.08.0.jar\r\n                                                                 [classpath] E:\\neo4j\\neo4j-community-2025.08.0-windows\\neo4j-community-2025.08.0\\lib\\neo4j-spatial-index-2025.08.0.jar\r\n                                                                 [classpath] E:\\neo4j\\neo4j-community-2025.08.0-windows\\neo4j-community-2025.08.0\\lib\\jetty-xml-12.0.17.jar"}
{"time":"2025-09-22 08:20:19.842+0000","level":"INFO","category":"o.n.i.d.DiagnosticsManager","message":"\r\n                                                                 [classpath] E:\\neo4j\\neo4j-community-2025.08.0-windows\\neo4j-community-2025.08.0\\lib\\neo4j-bolt-connection-netty-6.0.2.jar\r\n                                                                 [classpath] E:\\neo4j\\neo4j-community-2025.08.0-windows\\neo4j-community-2025.08.0\\lib\\neo4j-values-2025.08.0.jar\r\n                                                                 [classpath] E:\\neo4j\\neo4j-community-2025.08.0-windows\\neo4j-community-2025.08.0\\lib\\netty-buffer-4.2.4.Final.jar\r\n                                                                 [classpath] E:\\neo4j\\neo4j-community-2025.08.0-windows\\neo4j-community-2025.08.0\\lib\\cypher-preparser-2025.08.0.jar\r\n                                                                 [classpath] E:\\neo4j\\neo4j-community-2025.08.0-windows\\neo4j-community-2025.08.0\\lib\\annotations-2025.08.0.jar\r\n                                                                 [classpath] E:\\neo4j\\neo4j-community-2025.08.0-windows\\neo4j-community-2025.08.0\\lib\\jetty-server-12.0.17.jar\r\n                                                                 [classpath] E:\\neo4j\\neo4j-community-2025.08.0-windows\\neo4j-community-2025.08.0\\lib\\jaxb-runtime-2.3.2.jar\r\n                                                                 [classpath] E:\\neo4j\\neo4j-community-2025.08.0-windows\\neo4j-community-2025.08.0\\lib\\neo4j-notifications-2025.08.0.jar\r\n                                                                 [classpath] E:\\neo4j\\neo4j-community-2025.08.0-windows\\neo4j-community-2025.08.0\\lib\\lucene-backward-codecs-9.11.1.jar\r\n                                                                 [classpath] E:\\neo4j\\neo4j-community-2025.08.0-windows\\neo4j-community-2025.08.0\\lib\\WMI4Java-1.6.3.jar\r\n                                                                 [classpath] E:\\neo4j\\neo4j-community-2025.08.0-windows\\neo4j-community-2025.08.0\\lib\\slf4j-api-2.0.17.jar\r\n                                                                 [classpath] E:\\neo4j\\neo4j-community-2025.08.0-windows\\neo4j-community-2025.08.0\\lib\\netty-transport-native-epoll-4.2.4.Final-linux-x86_64.jar\r\n                                                                 [classpath] E:\\neo4j\\neo4j-community-2025.08.0-windows\\neo4j-community-2025.08.0\\lib\\jackson-annotations-2.19.1.jar\r\n                                                                 [classpath] E:\\neo4j\\neo4j-community-2025.08.0-windows\\neo4j-community-2025.08.0\\lib\\hk2-locator-2.6.1.jar\r\n                                                                 [classpath] E:\\neo4j\\neo4j-community-2025.08.0-windows\\neo4j-community-2025.08.0\\lib\\neo4j-indexcommands-2025.08.0.jar\r\n                                                                 [classpath] E:\\neo4j\\neo4j-community-2025.08.0-windows\\neo4j-community-2025.08.0\\lib\\cypher-v5-parser-listener-2025.08.0.jar\r\n                                                                 [classpath] E:\\neo4j\\neo4j-community-2025.08.0-windows\\neo4j-community-2025.08.0\\lib\\jackson-jaxrs-json-provider-2.19.1.jar\r\n                                                                 [classpath] E:\\neo4j\\neo4j-community-2025.08.0-windows\\neo4j-community-2025.08.0\\lib\\reactor-core-3.7.7.jar\r\n                                                                 [classpath] E:\\neo4j\\neo4j-community-2025.08.0-windows\\neo4j-community-2025.08.0\\lib\\proto-google-common-protos-2.51.0.jar\r\n                                                                 [classpath] E:\\neo4j\\neo4j-community-2025.08.0-windows\\neo4j-community-2025.08.0\\lib\\caffeine-3.2.1.jar\r\n                                                                 [classpath] E:\\neo4j\\neo4j-community-2025.08.0-windows\\neo4j-community-2025.08.0\\lib\\netty-transport-native-unix-common-4.2.4.Final.jar\r\n                                                                 [classpath] E:\\neo4j\\neo4j-community-2025.08.0-windows\\neo4j-community-2025.08.0\\lib\\netty-transport-native-kqueue-4.2.4.Final-osx-x86_64.jar\r\n                                                                 [classpath] E:\\neo4j\\neo4j-community-2025.08.0-windows\\neo4j-community-2025.08.0\\lib\\neo4j-cypher-physical-planning-2025.08.0.jar\r\n                                                                 [classpath] E:\\neo4j\\neo4j-community-2025.08.0-windows\\neo4j-community-2025.08.0\\lib\\shiro-config-core-2.0.5.jar\r\n                                                                 [classpath] E:\\neo4j\\neo4j-community-2025.08.0-windows\\neo4j-community-2025.08.0\\lib\\jakarta.xml.bind-api-2.3.2.jar\r\n                                                                 [classpath] E:\\neo4j\\neo4j-community-2025.08.0-windows\\neo4j-community-2025.08.0\\lib\\neo4j-cypher-logical-plans-2025.08.0.jar\r\n                                                                 [classpath] E:\\neo4j\\neo4j-community-2025.08.0-windows\\neo4j-community-2025.08.0\\lib\\scala-collection-contrib_2.13-0.3.0.jar\r\n                                                                 [classpath] E:\\neo4j\\neo4j-community-2025.08.0-windows\\neo4j-community-2025.08.0\\lib\\cypher-shell-2025.08.0.jar\r\n                                                                 [classpath] E:\\neo4j\\neo4j-community-2025.08.0-windows\\neo4j-community-2025.08.0\\lib\\grpc-core-1.73.0.jar\r\n                                                                 [classpath] E:\\neo4j\\neo4j-community-2025.08.0-windows\\neo4j-community-2025.08.0\\lib\\eclipse-collections-11.1.0.jar\r\n                                                                 [classpath] E:\\neo4j\\neo4j-community-2025.08.0-windows\\neo4j-community-2025.08.0\\lib\\netty-codec-base-4.2.4.Final.jar\r\n                                                                 [classpath] E:\\neo4j\\neo4j-community-2025.08.0-windows\\neo4j-community-2025.08.0\\lib\\javax.annotation-api-1.3.2.jar\r\n                                                                 [classpath] E:\\neo4j\\neo4j-community-2025.08.0-windows\\neo4j-community-2025.08.0\\lib\\neo4j-rewriting-2025.08.0.jar\r\n                                                                 [classpath] E:\\neo4j\\neo4j-community-2025.08.0-windows\\neo4j-community-2025.08.0\\lib\\javassist-3.30.2-GA.jar\r\n                                                                 [classpath] E:\\neo4j\\neo4j-community-2025.08.0-windows\\neo4j-community-2025.08.0\\lib\\commons-compress-1.27.1.jar\r\n                                                                 [classpath] E:\\neo4j\\neo4j-community-2025.08.0-windows\\neo4j-community-2025.08.0\\lib\\neo4j-bootcheck-2025.08.0.jar\r\n                                                                 [classpath] E:\\neo4j\\neo4j-community-2025.08.0-windows\\neo4j-community-2025.08.0\\lib\\neo4j-resource-2025.08.0.jar\r\n                                                                 [classpath] E:\\neo4j\\neo4j-community-2025.08.0-windows\\neo4j-community-2025.08.0\\lib\\neo4j-cypher-runtime-util-2025.08.0.jar\r\n                                                                 [classpath] E:\\neo4j\\neo4j-community-2025.08.0-windows\\neo4j-community-2025.08.0\\lib\\commons-codec-1.18.0.jar\r\n                                                                 [classpath] E:\\neo4j\\neo4j-community-2025.08.0-windows\\neo4j-community-2025.08.0\\plugins\\*\r\n                                                                 [classpath] E:\\neo4j\\neo4j-community-2025.08.0-windows\\neo4j-community-2025.08.0\\lib\\jetty-security-12.0.17.jar\r\n                                                                 [classpath] E:\\neo4j\\neo4j-community-2025.08.0-windows\\neo4j-community-2025.08.0\\lib\\arrow-memory-netty-18.3.0.jar\r\n                                                                 [classpath] E:\\neo4j\\neo4j-community-2025.08.0-windows\\neo4j-community-2025.08.0\\lib\\netty-transport-4.2.4.Final.jar\r\n                                                                 [classpath] E:\\neo4j\\neo4j-community-2025.08.0-windows\\neo4j-community-2025.08.0\\lib\\jetty-ee8-servlet-12.0.17.jar\r\n                                                                 [classpath] E:\\neo4j\\neo4j-community-2025.08.0-windows\\neo4j-community-2025.08.0\\lib\\snappy-java-1.1.10.7.jar\r\n                                                                 [classpath] E:\\neo4j\\neo4j-community-2025.08.0-windows\\neo4j-community-2025.08.0\\lib\\jettison-1.5.4.jar\r\n                                                                 [classpath] E:\\neo4j\\neo4j-community-2025.08.0-windows\\neo4j-community-2025.08.0\\lib\\commons-lang3-3.18.0.jar\r\n                                                                 [classpath] E:\\neo4j\\neo4j-community-2025.08.0-windows\\neo4j-community-2025.08.0\\lib\\log4j-layout-template-json-2.20.0.jar\r\n                                                                 [classpath] E:\\neo4j\\neo4j-community-2025.08.0-windows\\neo4j-community-2025.08.0\\lib\\flatbuffers-java-25.2.10.jar\r\n                                                                 [classpath] E:\\neo4j\\neo4j-community-2025.08.0-windows\\neo4j-community-2025.08.0\\lib\\jetty-alpn-java-server-12.0.17.jar"}
{"time":"2025-09-22 08:20:19.843+0000","level":"INFO","category":"o.n.i.d.DiagnosticsManager","message":"\r\n                                                                 [classpath] E:\\neo4j\\neo4j-community-2025.08.0-windows\\neo4j-community-2025.08.0\\lib\\neo4j-browser-ce-2025.8.0.jar\r\n                                                                 [classpath] E:\\neo4j\\neo4j-community-2025.08.0-windows\\neo4j-community-2025.08.0\\lib\\jetty-util-12.0.17.jar\r\n                                                                 [classpath] E:\\neo4j\\neo4j-community-2025.08.0-windows\\neo4j-community-2025.08.0\\lib\\cypher-v5-literal-interpreter-2025.08.0.jar\r\n                                                                 [classpath] E:\\neo4j\\neo4j-community-2025.08.0-windows\\neo4j-community-2025.08.0\\lib\\neo4j-gql-status-2025.08.0.jar\r\n                                                                 [classpath] E:\\neo4j\\neo4j-community-2025.08.0-windows\\neo4j-community-2025.08.0\\lib\\neo4j-slf4j-provider-2025.08.0.jar\r\n                                                                 [classpath] E:\\neo4j\\neo4j-community-2025.08.0-windows\\neo4j-community-2025.08.0\\lib\\netty-transport-classes-kqueue-4.2.4.Final.jar\r\n                                                                 [classpath] E:\\neo4j\\neo4j-community-2025.08.0-windows\\neo4j-community-2025.08.0\\lib\\neo4j-graph-algo-2025.08.0.jar\r\n                                                                 [classpath] E:\\neo4j\\neo4j-community-2025.08.0-windows\\neo4j-community-2025.08.0\\lib\\jetty-alpn-server-12.0.17.jar\r\n                                                                 [classpath] E:\\neo4j\\neo4j-community-2025.08.0-windows\\neo4j-community-2025.08.0\\lib\\scala-library-2.13.16.jar\r\n                                                                 [classpath] E:\\neo4j\\neo4j-community-2025.08.0-windows\\neo4j-community-2025.08.0\\lib\\aircompressor-2.0.2.jar\r\n                                                                 [classpath] E:\\neo4j\\neo4j-community-2025.08.0-windows\\neo4j-community-2025.08.0\\lib\\cypher-antlr-ast-common-2025.08.0.jar\r\n                                                                 [classpath] E:\\neo4j\\neo4j-community-2025.08.0-windows\\neo4j-community-2025.08.0\\lib\\protobuf-java-4.31.1.jar\r\n                                                                 [classpath] E:\\neo4j\\neo4j-community-2025.08.0-windows\\neo4j-community-2025.08.0\\lib\\jakarta.ws.rs-api-2.1.6.jar\r\n                                                                 [classpath] E:\\neo4j\\neo4j-community-2025.08.0-windows\\neo4j-community-2025.08.0\\lib\\neo4j-wal-2025.08.0.jar\r\n                                                                 [classpath] E:\\neo4j\\neo4j-community-2025.08.0-windows\\neo4j-community-2025.08.0\\lib\\jersey-common-2.43.jar\r\n                                                                 [classpath] E:\\neo4j\\neo4j-community-2025.08.0-windows\\neo4j-community-2025.08.0\\lib\\neo4j-import-util-2025.08.0.jar\r\n                                                                 [classpath] E:\\neo4j\\neo4j-community-2025.08.0-windows\\neo4j-community-2025.08.0\\lib\\neo4j-kernel-api-2025.08.0.jar\r\n                                                                 [classpath] E:\\neo4j\\neo4j-community-2025.08.0-windows\\neo4j-community-2025.08.0\\lib\\shiro-hashes-bcrypt-2.0.5.jar\r\n                                                                 [classpath] E:\\neo4j\\neo4j-community-2025.08.0-windows\\neo4j-community-2025.08.0\\lib\\parquet-common-1.15.2.jar\r\n                                                                 [classpath] E:\\neo4j\\neo4j-community-2025.08.0-windows\\neo4j-community-2025.08.0\\lib\\cypher-v25-antlr-parser-2025.08.0.jar\r\n                                                                 [classpath] E:\\neo4j\\neo4j-community-2025.08.0-windows\\neo4j-community-2025.08.0\\lib\\lucene-analysis-common-9.11.1.jar\r\n                                                                 [classpath] E:\\neo4j\\neo4j-community-2025.08.0-windows\\neo4j-community-2025.08.0\\lib\\neo4j-graphdb-api-2025.08.0.jar\r\n                                                                 [classpath] E:\\neo4j\\neo4j-community-2025.08.0-windows\\neo4j-community-2025.08.0\\lib\\ipaddress-5.5.1.jar\r\n                                                                 [classpath] E:\\neo4j\\neo4j-community-2025.08.0-windows\\neo4j-community-2025.08.0\\lib\\neo4j-procedure-2025.08.0.jar\r\n                                                                 [classpath] E:\\neo4j\\neo4j-community-2025.08.0-windows\\neo4j-community-2025.08.0\\lib\\netty-resolver-4.2.4.Final.jar\r\n                                                                 [classpath] E:\\neo4j\\neo4j-community-2025.08.0-windows\\neo4j-community-2025.08.0\\lib\\neo4j-query-router-2025.08.0.jar\r\n                                                                 [classpath] E:\\neo4j\\neo4j-community-2025.08.0-windows\\neo4j-community-2025.08.0\\lib\\netty-transport-classes-epoll-4.2.4.Final.jar\r\n                                                                 [classpath] E:\\neo4j\\neo4j-community-2025.08.0-windows\\neo4j-community-2025.08.0\\lib\\shiro-core-2.0.5.jar\r\n                                                                 [classpath] E:\\neo4j\\neo4j-community-2025.08.0-windows\\neo4j-community-2025.08.0\\lib\\jetty-session-12.0.17.jar\r\n                                                                 [classpath] E:\\neo4j\\neo4j-community-2025.08.0-windows\\neo4j-community-2025.08.0\\lib\\jetty-http2-common-12.0.17.jar\r\n                                                                 [classpath] E:\\neo4j\\neo4j-community-2025.08.0-windows\\neo4j-community-2025.08.0\\lib\\jetty-io-12.0.17.jar\r\n                                                                 [classpath] E:\\neo4j\\neo4j-community-2025.08.0-windows\\neo4j-community-2025.08.0\\lib\\neo4j-lock-2025.08.0.jar\r\n                                                                 [classpath] E:\\neo4j\\neo4j-community-2025.08.0-windows\\neo4j-community-2025.08.0\\lib\\neo4j-cypher-interpreted-runtime-2025.08.0.jar\r\n                                                                 [classpath] E:\\neo4j\\neo4j-community-2025.08.0-windows\\neo4j-community-2025.08.0\\lib\\asm-analysis-9.8.jar\r\n                                                                 [classpath] E:\\neo4j\\neo4j-community-2025.08.0-windows\\neo4j-community-2025.08.0\\lib\\jackson-core-2.19.1.jar\r\n                                                                 [classpath] E:\\neo4j\\neo4j-community-2025.08.0-windows\\neo4j-community-2025.08.0\\lib\\neo4j-server-2025.08.0.jar\r\n                                                                 [classpath] E:\\neo4j\\neo4j-community-2025.08.0-windows\\neo4j-community-2025.08.0\\lib\\neo4j-index-2025.08.0.jar\r\n                                                                 [classpath] E:\\neo4j\\neo4j-community-2025.08.0-windows\\neo4j-community-2025.08.0\\lib\\server-api-2025.08.0.jar\r\n                                                                 [classpath] E:\\neo4j\\neo4j-community-2025.08.0-windows\\neo4j-community-2025.08.0\\lib\\neo4j-bolt-connection-6.0.2.jar\r\n                                                                 [classpath] E:\\neo4j\\neo4j-community-2025.08.0-windows\\neo4j-community-2025.08.0\\lib\\perfmark-api-0.27.0.jar\r\n                                                                 [classpath] E:\\neo4j\\neo4j-community-2025.08.0-windows\\neo4j-community-2025.08.0\\lib\\netty-handler-proxy-4.2.4.Final.jar\r\n                                                                 [classpath] E:\\neo4j\\neo4j-community-2025.08.0-windows\\neo4j-community-2025.08.0\\lib\\jersey-container-servlet-core-2.43.jar\r\n                                                                 [classpath] E:\\neo4j\\neo4j-community-2025.08.0-windows\\neo4j-community-2025.08.0\\lib\\shiro-hashes-argon2-2.0.5.jar\r\n                                                                 [classpath] E:\\neo4j\\neo4j-community-2025.08.0-windows\\neo4j-community-2025.08.0\\lib\\netty-codec-compression-4.2.4.Final.jar\r\n                                                                 [classpath] E:\\neo4j\\neo4j-community-2025.08.0-windows\\neo4j-community-2025.08.0\\lib\\failureaccess-1.0.3.jar\r\n                                                                 [classpath] E:\\neo4j\\neo4j-community-2025.08.0-windows\\neo4j-community-2025.08.0\\lib\\eclipse-collections-api-11.1.0.jar\r\n                                                                 [classpath] E:\\neo4j\\neo4j-community-2025.08.0-windows\\neo4j-community-2025.08.0\\lib\\neo4j-csv-2025.08.0.jar\r\n                                                                 [classpath] E:\\neo4j\\neo4j-community-2025.08.0-windows\\neo4j-community-2025.08.0\\lib\\picocli-4.7.7.jar\r\n                                                                 [classpath] E:\\neo4j\\neo4j-community-2025.08.0-windows\\neo4j-community-2025.08.0\\lib\\netty-codec-socks-4.2.4.Final.jar\r\n                                                                 [classpath] E:\\neo4j\\neo4j-community-2025.08.0-windows\\neo4j-community-2025.08.0\\lib\\neo4j-import-api-2025.08.0.jar"}
{"time":"2025-09-22 08:20:19.845+0000","level":"INFO","category":"o.n.i.d.DiagnosticsManager","message":"\r\n                                                                 [classpath] E:\\neo4j\\neo4j-community-2025.08.0-windows\\neo4j-community-2025.08.0\\lib\\protobuf-java-util-4.31.1.jar\r\n                                                                 [classpath] E:\\neo4j\\neo4j-community-2025.08.0-windows\\neo4j-community-2025.08.0\\lib\\parquet-format-structures-1.15.2.jar\r\n                                                                 [classpath] E:\\neo4j\\neo4j-community-2025.08.0-windows\\neo4j-community-2025.08.0\\lib\\jackson-module-jaxb-annotations-2.19.1.jar\r\n                                                                 [classpath] E:\\neo4j\\neo4j-community-2025.08.0-windows\\neo4j-community-2025.08.0\\lib\\neo4j-cypher-rendering-2025.08.0.jar\r\n                                                                 [classpath] E:\\neo4j\\neo4j-community-2025.08.0-windows\\neo4j-community-2025.08.0\\lib\\kiama_2.13-2.5.1.jar\r\n                                                                 [classpath] E:\\neo4j\\neo4j-community-2025.08.0-windows\\neo4j-community-2025.08.0\\lib\\shiro-crypto-hash-2.0.5.jar\r\n                                                                 [classpath] E:\\neo4j\\neo4j-community-2025.08.0-windows\\neo4j-community-2025.08.0\\lib\\grpc-context-1.73.0.jar\r\n                                                                 [classpath] E:\\neo4j\\neo4j-community-2025.08.0-windows\\neo4j-community-2025.08.0\\lib\\neo4j-concurrent-2025.08.0.jar\r\n                                                                 [classpath] E:\\neo4j\\neo4j-community-2025.08.0-windows\\neo4j-community-2025.08.0\\lib\\istack-commons-runtime-3.0.8.jar\r\n                                                                 [classpath] E:\\neo4j\\neo4j-community-2025.08.0-windows\\neo4j-community-2025.08.0\\lib\\jetty-ee-12.0.17.jar\r\n                                                                 [classpath] E:\\neo4j\\neo4j-community-2025.08.0-windows\\neo4j-community-2025.08.0\\lib\\netty-tcnative-classes-2.0.72.Final.jar\r\n                                                                 [classpath] E:\\neo4j\\neo4j-community-2025.08.0-windows\\neo4j-community-2025.08.0\\lib\\cypher-parser-factory-2025.08.0.jar\r\n                                                                 [classpath] E:\\neo4j\\neo4j-community-2025.08.0-windows\\neo4j-community-2025.08.0\\lib\\neo4j-bolt-connection-pooled-6.0.2.jar\r\n                                                                 [classpath] E:\\neo4j\\neo4j-community-2025.08.0-windows\\neo4j-community-2025.08.0\\lib\\commons-io-2.19.0.jar\r\n                                                                 [classpath] E:\\neo4j\\neo4j-community-2025.08.0-windows\\neo4j-community-2025.08.0\\lib\\shiro-crypto-core-2.0.5.jar\r\n                                                                 [classpath] E:\\neo4j\\neo4j-community-2025.08.0-windows\\neo4j-community-2025.08.0\\lib\\lucene-core-9.11.1.jar\r\n                                                                 [classpath] E:\\neo4j\\neo4j-community-2025.08.0-windows\\neo4j-community-2025.08.0\\lib\\neo4j-common-2025.08.0.jar\r\n                                                                 [classpath] E:\\neo4j\\neo4j-community-2025.08.0-windows\\neo4j-community-2025.08.0\\lib\\neo4j-cloud-2025.08.0.jar\r\n                                                                 [classpath] E:\\neo4j\\neo4j-community-2025.08.0-windows\\neo4j-community-2025.08.0\\lib\\zstd-proxy-2025.08.0.jar\r\n                                                                 [classpath] E:\\neo4j\\neo4j-community-2025.08.0-windows\\neo4j-community-2025.08.0\\lib\\hk2-utils-2.6.1.jar\r\n                                                                 [classpath] E:\\neo4j\\neo4j-community-2025.08.0-windows\\neo4j-community-2025.08.0\\lib\\neo4j-id-generator-2025.08.0.jar\r\n                                                                 [classpath] E:\\neo4j\\neo4j-community-2025.08.0-windows\\neo4j-community-2025.08.0\\lib\\netty-common-4.2.4.Final.jar\r\n                                                                 [classpath] E:\\neo4j\\neo4j-community-2025.08.0-windows\\neo4j-community-2025.08.0\\lib\\jline-terminal-3.21.0.jar\r\n                                                                 [classpath] E:\\neo4j\\neo4j-community-2025.08.0-windows\\neo4j-community-2025.08.0\\lib\\neo4j-cypher-ir-2025.08.0.jar\r\n                                                                 [classpath] E:\\neo4j\\neo4j-community-2025.08.0-windows\\neo4j-community-2025.08.0\\lib\\neo4j-configuration-2025.08.0.jar\r\n                                                                 [classpath] E:\\neo4j\\neo4j-community-2025.08.0-windows\\neo4j-community-2025.08.0\\lib\\asm-9.8.jar\r\n                                                                 [classpath] E:\\neo4j\\neo4j-community-2025.08.0-windows\\neo4j-community-2025.08.0\\lib\\lucene-queryparser-9.11.1.jar\r\n                                                                 [classpath] E:\\neo4j\\neo4j-community-2025.08.0-windows\\neo4j-community-2025.08.0\\lib\\neo4j-io-2025.08.0.jar\r\n                                                                 [classpath] E:\\neo4j\\neo4j-community-2025.08.0-windows\\neo4j-community-2025.08.0\\lib\\gson-2.11.0.jar\r\n                                                                 [classpath] E:\\neo4j\\neo4j-community-2025.08.0-windows\\neo4j-community-2025.08.0\\lib\\cypher-v5-antlr-parser-2025.08.0.jar\r\n                                                                 [classpath] E:\\neo4j\\neo4j-community-2025.08.0-windows\\neo4j-community-2025.08.0\\lib\\neo4j-push-to-cloud-2025.08.0.jar\r\n                                                                 [classpath] E:\\neo4j\\neo4j-community-2025.08.0-windows\\neo4j-community-2025.08.0\\lib\\commons-text-1.13.1.jar\r\n                                                                 [classpath] E:\\neo4j\\neo4j-community-2025.08.0-windows\\neo4j-community-2025.08.0\\lib\\argparse4j-0.9.0.jar\r\n                                                                 [classpath] E:\\neo4j\\neo4j-community-2025.08.0-windows\\neo4j-community-2025.08.0\\lib\\netty-handler-4.2.4.Final.jar\r\n                                                                 [classpath] E:\\neo4j\\neo4j-community-2025.08.0-windows\\neo4j-community-2025.08.0\\lib\\neo4j-storage-engine-util-2025.08.0.jar\r\n                                                                 [classpath] E:\\neo4j\\neo4j-community-2025.08.0-windows\\neo4j-community-2025.08.0\\lib\\jline-reader-3.21.0.jar\r\n                                                                 [classpath] E:\\neo4j\\neo4j-community-2025.08.0-windows\\neo4j-community-2025.08.0\\lib\\neo4j-cypher-macros-2025.08.0.jar\r\n                                                                 [classpath] E:\\neo4j\\neo4j-community-2025.08.0-windows\\neo4j-community-2025.08.0\\lib\\jProcesses-1.6.5.jar\r\n                                                                 [classpath] E:\\neo4j\\neo4j-community-2025.08.0-windows\\neo4j-community-2025.08.0\\lib\\log4j-core-2.20.0.jar\r\n                                                                 [classpath] E:\\neo4j\\neo4j-community-2025.08.0-windows\\neo4j-community-2025.08.0\\lib\\neo4j-import-tool-2025.08.0.jar\r\n                                                                 [classpath] E:\\neo4j\\neo4j-community-2025.08.0-windows\\neo4j-community-2025.08.0\\lib\\neo4j-cypher-expression-evaluator-2025.08.0.jar\r\n                                                                 [classpath] E:\\neo4j\\neo4j-community-2025.08.0-windows\\neo4j-community-2025.08.0\\lib\\jetty-http-12.0.17.jar\r\n                                                                 [classpath] E:\\neo4j\\neo4j-community-2025.08.0-windows\\neo4j-community-2025.08.0\\lib\\jakarta.inject-2.6.1.jar\r\n                                                                 [classpath] E:\\neo4j\\neo4j-community-2025.08.0-windows\\neo4j-community-2025.08.0\\lib\\jetty-servlet-api-4.0.6.jar\r\n                                                                 [classpath] E:\\neo4j\\neo4j-community-2025.08.0-windows\\neo4j-community-2025.08.0\\lib\\neo4j-schema-2025.08.0.jar\r\n                                                                 [classpath] E:\\neo4j\\neo4j-community-2025.08.0-windows\\neo4j-community-2025.08.0\\lib\\jetty-ee8-security-12.0.17.jar\r\n                                                                 [classpath] E:\\neo4j\\neo4j-community-2025.08.0-windows\\neo4j-community-2025.08.0\\lib\\grpc-protobuf-lite-1.73.0.jar\r\n                                                                 [classpath] E:\\neo4j\\neo4j-community-2025.08.0-windows\\neo4j-community-2025.08.0\\lib\\grpc-api-1.73.0.jar\r\n                                                                 [classpath] E:\\neo4j\\neo4j-community-2025.08.0-windows\\neo4j-community-2025.08.0\\lib\\shiro-cache-2.0.5.jar\r\n                                                                 [classpath] E:\\neo4j\\neo4j-community-2025.08.0-windows\\neo4j-community-2025.08.0\\lib\\neo4j-monitoring-2025.08.0.jar"}
{"time":"2025-09-22 08:20:19.847+0000","level":"INFO","category":"o.n.i.d.DiagnosticsManager","message":"\r\n                                                                 [classpath] E:\\neo4j\\neo4j-community-2025.08.0-windows\\neo4j-community-2025.08.0\\lib\\netty-codec-http-4.2.4.Final.jar\r\n                                                                 [classpath] E:\\neo4j\\neo4j-community-2025.08.0-windows\\neo4j-community-2025.08.0\\lib\\grpc-stub-1.73.0.jar\r\n                                                                 [classpath] E:\\neo4j\\neo4j-community-2025.08.0-windows\\neo4j-community-2025.08.0\\lib\\hk2-api-2.6.1.jar\r\n                                                                 [classpath] E:\\neo4j\\neo4j-community-2025.08.0-windows\\neo4j-community-2025.08.0\\lib\\neo4j-security-2025.08.0.jar\r\n                                                                 [classpath] E:\\neo4j\\neo4j-community-2025.08.0-windows\\neo4j-community-2025.08.0\\lib\\neo4j-logging-2025.08.0.jar\r\n                                                                 [classpath] E:\\neo4j\\neo4j-community-2025.08.0-windows\\neo4j-community-2025.08.0\\lib\\arrow-memory-core-18.3.0.jar\r\n                                                                 [classpath] E:\\neo4j\\neo4j-community-2025.08.0-windows\\neo4j-community-2025.08.0\\lib\\neo4j-ssl-2025.08.0.jar\r\n                                                                 [classpath] E:\\neo4j\\neo4j-community-2025.08.0-windows\\neo4j-community-2025.08.0\\lib\\FastInfoset-1.2.16.jar\r\n                                                                "}
{"time":"2025-09-22 08:20:19.847+0000","level":"INFO","category":"o.n.i.d.DiagnosticsManager","message":"\r\n                                                                --------------------------------------------------------------------------------\r\n                                                                                                [ Library path ]                                \r\n                                                                --------------------------------------------------------------------------------\r\n                                                                E:\\java\\bin\r\n                                                                C:\\Windows\\Sun\\Java\\bin\r\n                                                                C:\\Windows\\System32\r\n                                                                C:\\Windows\r\n                                                                E:\\conda\r\n                                                                E:\\conda\\Library\\mingw-w64\\bin\r\n                                                                E:\\conda\\Library\\usr\\bin\r\n                                                                E:\\conda\\Library\\bin\r\n                                                                E:\\conda\\Scripts\r\n                                                                E:\\conda\\bin\r\n                                                                C:\\Windows\\System32\r\n                                                                C:\\Windows\r\n                                                                C:\\Windows\\System32\\wbem\r\n                                                                C:\\Windows\\System32\\WindowsPowerShell\\v1.0\r\n                                                                C:\\Windows\\System32\\OpenSSH\r\n                                                                C:\\Program Files\\Docker\\Docker\\resources\\bin\r\n                                                                D:\\trae\r\n                                                                D:\\node.js\r\n                                                                D:\\git\r\n                                                                D:\\git\\git\\Git\\cmd\r\n                                                                C:\\Program Files\\dotnet\r\n                                                                C:\\Users\\<USER>\\AppData\\Roaming\\npm\r\n                                                                C:\\Users\\<USER>\\AppData\\Local\\Muse Hub\\lib\r\n                                                                C:\\ProgramData\\chocolatey\\bin\r\n                                                                C:\\Program Files\\gs\\gs10.05.1\\bin\r\n                                                                C:\\ProgramData\\chocolatey\\lib\\poppler\\tools\\poppler-25.07.0\\bin\r\n                                                                C:\\Users\\<USER>\\.trae\\sdks\\workspaces\\c47c7beb\\versions\\node\\current\r\n                                                                C:\\Users\\<USER>\\.trae\\sdks\\versions\\node\\current\r\n                                                                C:\\Users\\<USER>\\.trae\\sdks\\workspaces\\c47c7beb\\versions\\node\\current\r\n                                                                C:\\Users\\<USER>\\.trae\\sdks\\versions\\node\\current\r\n                                                                C:\\Windows\\System32\r\n                                                                C:\\Windows\r\n                                                                C:\\Windows\\System32\\wbem\r\n                                                                C:\\Windows\\System32\\WindowsPowerShell\\v1.0\r\n                                                                C:\\Windows\\System32\\OpenSSH\r\n                                                                C:\\Program Files\\Docker\\Docker\\resources\\bin\r\n                                                                D:\\trae\r\n                                                                D:\\node.js\r\n                                                                D:\\git\r\n                                                                D:\\git\\git\\Git\\cmd\r\n                                                                C:\\Program Files\\dotnet\r\n                                                                C:\\Users\\<USER>\\AppData\\Roaming\\npm\r\n                                                                C:\\Users\\<USER>\\AppData\\Local\\Muse Hub\\lib\r\n                                                                C:\\ProgramData\\chocolatey\\bin\r\n                                                                C:\\Program Files\\gs\\gs10.05.1\\bin\r\n                                                                C:\\ProgramData\\chocolatey\\lib\\poppler\\tools\\poppler-25.07.0\\bin\r\n                                                                E:\\python\\Scripts\r\n                                                                E:\\python\r\n                                                                C:\\Users\\<USER>\\AppData\\Local\\Microsoft\\WindowsApps\r\n                                                                D:\\git\r\n                                                                C:\\Users\\<USER>\\AppData\\Local\\Muse Hub\\lib\r\n                                                                E:\\VSCode\\Microsoft VS Code\\bin\r\n                                                                C:\\Users\\<USER>\\AppData\\Roaming\\npm\r\n                                                                C:\\Users\\<USER>\\AppData\\Local\\Pandoc\r\n                                                                C:\\Program Files\\Tesseract-OCR\r\n                                                                C:\\ProgramData\\chocolatey\\lib\\poppler\\tools\\poppler-25.07.0\\bin\r\n                                                                E:\\kiro\\Kiro\\bin\r\n                                                                C:\\Users\\<USER>\\.trae\\extensions\\ms-python.debugpy-2025.6.0-win32-x64\\bundled\\scripts\\noConfigScripts\r\n                                                                E:\\conda\r\n                                                                E:\\conda\\Scripts\r\n                                                                E:\\conda\\condabin\r\n                                                                E:\\cursor\\cursor\\resources\\app\\bin\r\n                                                                C:\\Users\\<USER>\\AppData\\Local\\Programs\\Ollama\r\n                                                                E:\\neo4j\\neo4j-community-2025.08.0-windows\\neo4j-community-2025.08.0\r\n                                                                "}
{"time":"2025-09-22 08:20:19.848+0000","level":"INFO","category":"o.n.i.d.DiagnosticsManager","message":"\r\n                                                                --------------------------------------------------------------------------------\r\n                                                                                             [ System properties ]                              \r\n                                                                --------------------------------------------------------------------------------\r\n                                                                jdk.tls.rejectClientInitiatedRenegotiation = true\r\n                                                                sun.cpu.isalist = amd64\r\n                                                                sun.jnu.encoding = GBK\r\n                                                                log4j.layout.jsonTemplate.maxStringLength = 32768\r\n                                                                sun.arch.data.model = 64\r\n                                                                user.variant = \r\n                                                                user.timezone = Asia/Shanghai\r\n                                                                io.netty.leakDetection.level = DISABLED\r\n                                                                sun.java.launcher = SUN_STANDARD\r\n                                                                user.country = CN\r\n                                                                sun.boot.library.path = E:\\java\\bin\r\n                                                                sun.java.command = org.neo4j.server.Neo4jCommunity --home-dir=E:\\neo4j\\neo4j-community-2025.08.0-windows\\neo4j-community-2025.08.0 --config-dir=E:\\neo4j\\neo4j-community-2025.08.0-windows\\neo4j-community-2025.08.0\\conf --console-mode\r\n                                                                jdk.debug = release\r\n                                                                io.netty.tryReflectionSetAccessible = true\r\n                                                                sun.cpu.endian = little\r\n                                                                user.home = C:\\Users\\<USER>\r\n                                                                user.language = zh\r\n                                                                file.separator = \\\r\n                                                                jdk.tls.ephemeralDHKeySize = 2048\r\n                                                                user.script = \r\n                                                                sun.management.compiler = HotSpot 64-Bit Tiered Compilers\r\n                                                                user.name = Administrator\r\n                                                                stdout.encoding = ms936\r\n                                                                jdk.nio.maxCachedBufferSize = 1024\r\n                                                                path.separator = ;\r\n                                                                file.encoding = UTF-8\r\n                                                                jnidispatch.path = C:\\Users\\<USER>\\AppData\\Local\\Temp\\jna-146731693\\jna11647534780167126012.dll\r\n                                                                jna.loaded = true\r\n                                                                user.dir = E:\\neo4j\\neo4j-community-2025.08.0-windows\\neo4j-community-2025.08.0\r\n                                                                sun.os.patch.level = \r\n                                                                native.encoding = GBK\r\n                                                                stderr.encoding = ms936\r\n                                                                sun.io.unicode.encoding = UnicodeLittle\r\n                                                                log4j2.disable.jmx = true\r\n                                                                "}
{"time":"2025-09-22 08:20:19.848+0000","level":"INFO","category":"o.n.i.d.DiagnosticsManager","message":"\r\n                                                                --------------------------------------------------------------------------------\r\n                                                                                      [ (IANA) TimeZone database version ]                      \r\n                                                                --------------------------------------------------------------------------------\r\n                                                                  TimeZone version: 2025b (available for 604 zone identifiers)\r\n                                                                "}
{"time":"2025-09-22 08:20:19.848+0000","level":"INFO","category":"o.n.i.d.DiagnosticsManager","message":"\r\n                                                                --------------------------------------------------------------------------------\r\n                                                                                            [ Network information ]                             \r\n                                                                --------------------------------------------------------------------------------\r\n                                                                Interface WAN Miniport (IP)-WFP Native MAC Layer LightWeight Filter-0000:\r\n                                                                Interface WAN Miniport (IP)-QoS Packet Scheduler-0000:\r\n                                                                Interface WAN Miniport (IPv6)-WFP Native MAC Layer LightWeight Filter-0000:\r\n                                                                Interface Hyper-V Virtual Switch Extension Adapter-Hyper-V Virtual Switch Extension Filter-0000:\r\n                                                                Interface WAN Miniport (IPv6)-QoS Packet Scheduler-0000:\r\n                                                                Interface Hyper-V Virtual Ethernet Adapter-WFP Native MAC Layer LightWeight Filter-0000:\r\n                                                                Interface Hyper-V Virtual Ethernet Adapter-QoS Packet Scheduler-0000:\r\n                                                                Interface WAN Miniport (Network Monitor)-WFP Native MAC Layer LightWeight Filter-0000:\r\n                                                                Interface Realtek PCIe GbE Family Controller-WFP Native MAC Layer LightWeight Filter-0000:\r\n                                                                Interface Realtek PCIe GbE Family Controller-QoS Packet Scheduler-0000:\r\n                                                                Interface Realtek PCIe GbE Family Controller-WFP 802.3 MAC Layer LightWeight Filter-0000:\r\n                                                                Interface WAN Miniport (Network Monitor)-QoS Packet Scheduler-0000:\r\n                                                                Interface Hyper-V Virtual Ethernet Adapter-WFP 802.3 MAC Layer LightWeight Filter-0000:\r\n                                                                Interface Hyper-V Virtual Switch Extension Adapter #2-Hyper-V Virtual Switch Extension Filter-0000:\r\n                                                                Interface Hyper-V Virtual Ethernet Adapter #2-WFP Native MAC Layer LightWeight Filter-0000:\r\n                                                                Interface Hyper-V Virtual Ethernet Adapter #2-QoS Packet Scheduler-0000:\r\n                                                                Interface Hyper-V Virtual Ethernet Adapter #2-WFP 802.3 MAC Layer LightWeight Filter-0000:\r\n                                                                Interface Microsoft Kernel Debug Network Adapter:\r\n                                                                Interface Realtek PCIe GbE Family Controller:\r\n                                                                    address: fe80:0:0:0:c25a:79:75e8:6d90%ethernet_32769\r\n                                                                Interface Bluetooth Device (Personal Area Network):\r\n                                                                Interface WAN Miniport (IP):\r\n                                                                Interface WAN Miniport (IPv6):\r\n                                                                Interface WAN Miniport (Network Monitor):\r\n                                                                Interface Hyper-V Virtual Switch Extension Adapter:\r\n                                                                Interface Hyper-V Virtual Ethernet Adapter:\r\n                                                                    address: fe80:0:0:0:1a68:e38c:f878:e658%ethernet_32775\r\n                                                                    address: *************\r\n                                                                Interface Hyper-V Virtual Switch Extension Adapter #2:\r\n                                                                Interface Hyper-V Virtual Ethernet Adapter #2:\r\n                                                                    address: fe80:0:0:0:4929:732b:f43f:aa4c%ethernet_32777\r\n                                                                    address: ************\r\n                                                                Interface WAN Miniport (PPPOE):\r\n                                                                Interface Software Loopback Interface 1:\r\n                                                                    address: 0:0:0:0:0:0:0:1\r\n                                                                    address: 127.0.0.1\r\n                                                                Interface Meta Tunnel:\r\n                                                                    address: fdfe:dcba:9876:0:0:0:0:1\r\n                                                                    address: **********\r\n                                                                Interface Intel(R) Wi-Fi 6 AX200 160MHz-WFP Native MAC Layer LightWeight Filter-0000:\r\n                                                                Interface Intel(R) Wi-Fi 6 AX200 160MHz-Virtual WiFi Filter Driver-0000:\r\n                                                                Interface Intel(R) Wi-Fi 6 AX200 160MHz-Native WiFi Filter Driver-0000:\r\n                                                                Interface Intel(R) Wi-Fi 6 AX200 160MHz-QoS Packet Scheduler-0000:\r\n                                                                Interface Intel(R) Wi-Fi 6 AX200 160MHz-WFP 802.3 MAC Layer LightWeight Filter-0000:\r\n                                                                Interface Microsoft Wi-Fi Direct Virtual Adapter-WFP Native MAC Layer LightWeight Filter-0000:\r\n                                                                Interface Microsoft Wi-Fi Direct Virtual Adapter-Native WiFi Filter Driver-0000:\r\n                                                                Interface Microsoft Wi-Fi Direct Virtual Adapter-QoS Packet Scheduler-0000:\r\n                                                                Interface Microsoft Wi-Fi Direct Virtual Adapter-WFP 802.3 MAC Layer LightWeight Filter-0000:\r\n                                                                Interface Microsoft Wi-Fi Direct Virtual Adapter #2-WFP Native MAC Layer LightWeight Filter-0000:\r\n                                                                Interface Microsoft Wi-Fi Direct Virtual Adapter #2-Native WiFi Filter Driver-0000:\r\n                                                                Interface Microsoft Wi-Fi Direct Virtual Adapter #2-QoS Packet Scheduler-0000:\r\n                                                                Interface Microsoft Wi-Fi Direct Virtual Adapter #2-WFP 802.3 MAC Layer LightWeight Filter-0000:\r\n                                                                Interface Intel(R) Wi-Fi 6 AX200 160MHz:\r\n                                                                    address: fe80:0:0:0:592e:4e99:26e4:b1f5%wireless_32768\r\n                                                                    address: 2408:820c:820a:1760:9ea1:537b:5307:24e\r\n                                                                    address: 2408:820c:820a:1760:206d:eb1e:620:c820\r\n                                                                    address: 2408:820c:820a:1760:0:0:0:6fa\r\n                                                                    address: ************\r\n                                                                Interface Microsoft Wi-Fi Direct Virtual Adapter:\r\n                                                                    address: fe80:0:0:0:dcb:7296:b3b8:d60b%wireless_32769\r\n                                                                Interface Microsoft Wi-Fi Direct Virtual Adapter #2:\r\n                                                                    address: fe80:0:0:0:11f0:af32:4c68:7d36%wireless_32770\r\n                                                                Interface Microsoft Teredo Tunneling Adapter:\r\n                                                                Interface Microsoft IP-HTTPS Platform Adapter:\r\n                                                                Interface Microsoft 6to4 Adapter:\r\n                                                                Interface WAN Miniport (SSTP):\r\n                                                                Interface WAN Miniport (IKEv2):\r\n                                                                Interface WAN Miniport (L2TP):\r\n                                                                Interface WAN Miniport (PPTP):\r\n                                                                "}
{"time":"2025-09-22 08:20:19.848+0000","level":"INFO","category":"o.n.i.d.DiagnosticsManager","message":"\r\n                                                                --------------------------------------------------------------------------------\r\n                                                                                         [ Native access information ]                          \r\n                                                                --------------------------------------------------------------------------------\r\n                                                                Native access details: Native access is not available for current platform.\r\n                                                                "}
{"time":"2025-09-22 08:20:19.848+0000","level":"INFO","category":"o.n.i.d.DiagnosticsManager","message":"\r\n                                                                --------------------------------------------------------------------------------\r\n                                                                                                [ DBMS config ]                                 \r\n                                                                --------------------------------------------------------------------------------\r\n                                                                DBMS provided settings:\r\n                                                                internal.dbms.bolt.local_address=be39d82f-a426-44bd-ad7d-794aecdc66af\r\n                                                                server.bolt.enabled=true\r\n                                                                server.directories.configuration=E:\\neo4j\\neo4j-community-2025.08.0-windows\\neo4j-community-2025.08.0\\conf\r\n                                                                server.directories.import=E:\\neo4j\\neo4j-community-2025.08.0-windows\\neo4j-community-2025.08.0\\import\r\n                                                                server.directories.neo4j_home=E:\\neo4j\\neo4j-community-2025.08.0-windows\\neo4j-community-2025.08.0\r\n                                                                server.http.enabled=true\r\n                                                                server.https.enabled=false\r\n                                                                server.jvm.additional=-XX:+UseG1GC\r\n-XX:-OmitStackTraceInFastThrow\r\n-XX:+AlwaysPreTouch\r\n-XX:+UnlockExperimentalVMOptions\r\n-XX:+TrustFinalNonStaticFields\r\n-XX:+DisableExplicitGC\r\n-Djdk.nio.maxCachedBufferSize=1024\r\n-Dio.netty.tryReflectionSetAccessible=true\r\n-Dio.netty.leakDetection.level=DISABLED\r\n-Djdk.tls.ephemeralDHKeySize=2048\r\n-Djdk.tls.rejectClientInitiatedRenegotiation=true\r\n-XX:FlightRecorderOptions=stackdepth=256\r\n-XX:+UnlockDiagnosticVMOptions\r\n-XX:+DebugNonSafepoints\r\n--add-opens=java.base/java.nio=ALL-UNNAMED\r\n--add-opens=java.base/java.io=ALL-UNNAMED\r\n--add-opens=java.base/sun.nio.ch=ALL-UNNAMED\r\n--add-opens=java.base/java.util.concurrent=ALL-UNNAMED\r\n--enable-native-access=ALL-UNNAMED\r\n-Dlog4j2.disable.jmx=true\r\n-Dlog4j.layout.jsonTemplate.maxStringLength=32768\r\n                                                                server.windows_service_name=neo4j\r\n                                                                Directories in use:\r\n                                                                server.directories.configuration=E:\\neo4j\\neo4j-community-2025.08.0-windows\\neo4j-community-2025.08.0\\conf\r\n                                                                server.directories.data=E:\\neo4j\\neo4j-community-2025.08.0-windows\\neo4j-community-2025.08.0\\data\r\n                                                                server.directories.dumps.root=E:\\neo4j\\neo4j-community-2025.08.0-windows\\neo4j-community-2025.08.0\\data\\dumps\r\n                                                                server.directories.import=E:\\neo4j\\neo4j-community-2025.08.0-windows\\neo4j-community-2025.08.0\\import\r\n                                                                server.directories.lib=E:\\neo4j\\neo4j-community-2025.08.0-windows\\neo4j-community-2025.08.0\\lib\r\n                                                                server.directories.licenses=E:\\neo4j\\neo4j-community-2025.08.0-windows\\neo4j-community-2025.08.0\\licenses\r\n                                                                server.directories.logs=E:\\neo4j\\neo4j-community-2025.08.0-windows\\neo4j-community-2025.08.0\\logs\r\n                                                                server.directories.plugins=E:\\neo4j\\neo4j-community-2025.08.0-windows\\neo4j-community-2025.08.0\\plugins\r\n                                                                server.directories.run=E:\\neo4j\\neo4j-community-2025.08.0-windows\\neo4j-community-2025.08.0\\run\r\n                                                                server.directories.script.root=E:\\neo4j\\neo4j-community-2025.08.0-windows\\neo4j-community-2025.08.0\\data\\scripts\r\n                                                                server.directories.transaction.logs.root=E:\\neo4j\\neo4j-community-2025.08.0-windows\\neo4j-community-2025.08.0\\data\\transactions\r\n                                                                "}
{"time":"2025-09-22 08:20:19.848+0000","level":"INFO","category":"o.n.i.d.DiagnosticsManager","message":"\r\n                                                                --------------------------------------------------------------------------------\r\n                                                                                                 [ Packaging ]                                  \r\n                                                                --------------------------------------------------------------------------------\r\n                                                                Edition: Community\r\n                                                                Package Type: tar\r\n                                                                "}
{"time":"2025-09-22 08:20:19.848+0000","level":"INFO","category":"o.n.i.d.DiagnosticsManager","message":"\r\n                                                                --------------------------------------------------------------------------------\r\n                                                                                           [ Global Server Identity ]                           \r\n                                                                --------------------------------------------------------------------------------\r\n                                                                Registered ServerId{b35704b1}\r\n                                                                "}
{"time":"2025-09-22 08:20:20.324+0000","level":"INFO","category":"o.n.b.BoltServer","message":"Using connector transport NIO"}
{"time":"2025-09-22 08:20:20.596+0000","level":"INFO","category":"o.n.b.BoltServer","message":"Configured external Bolt connector with listener address localhost/127.0.0.1:7687"}
{"time":"2025-09-22 08:20:20.601+0000","level":"INFO","category":"o.n.b.BoltServer","message":"Bolt server loaded"}
{"time":"2025-09-22 08:20:20.648+0000","level":"INFO","category":"o.n.d.d.DatabaseLifecycles","message":"Starting 'DatabaseId{00000000[system]}'."}
{"time":"2025-09-22 08:20:21.635+0000","level":"INFO","category":"o.n.k.i.t.l.f.c.CheckpointLogFile","message":"[system/00000000] Scanning log file with version 0 for checkpoint entries","databaseId":"00000000-0000-0000-0000-000000000001","databaseName":"system"}
{"time":"2025-09-22 08:20:21.708+0000","level":"INFO","category":"o.n.k.r.Recovery","message":"[system/00000000] Requirement `Database unavailable` makes database system unavailable.","databaseId":"00000000-0000-0000-0000-000000000001","databaseName":"system"}
{"time":"2025-09-22 08:20:21.709+0000","level":"INFO","category":"o.n.k.r.Recovery","message":"[system/00000000] DatabaseId{00000000[system]} is unavailable.","databaseId":"00000000-0000-0000-0000-000000000001","databaseName":"system"}
{"time":"2025-09-22 08:20:21.710+0000","level":"INFO","category":"o.n.k.r.Recovery","message":"[system/00000000] Fulfilling of requirement 'Database unavailable' makes database system available.","databaseId":"00000000-0000-0000-0000-000000000001","databaseName":"system"}
{"time":"2025-09-22 08:20:21.710+0000","level":"INFO","category":"o.n.k.r.Recovery","message":"[system/00000000] DatabaseId{00000000[system]} is ready.","databaseId":"00000000-0000-0000-0000-000000000001","databaseName":"system"}
{"time":"2025-09-22 08:20:21.741+0000","level":"INFO","category":"o.n.k.i.s.f.RecordFormatSelector","message":"[system/00000000] Selected RecordFormat:PageAlignedV5_0[aligned-1.1] record format from store E:\\neo4j\\neo4j-community-2025.08.0-windows\\neo4j-community-2025.08.0\\data\\databases\\system","databaseId":"00000000-0000-0000-0000-000000000001","databaseName":"system"}
{"time":"2025-09-22 08:20:21.742+0000","level":"INFO","category":"o.n.k.i.s.f.RecordFormatSelector","message":"[system/00000000] Selected format from the store files: RecordFormat:PageAlignedV5_0[aligned-1.1]","databaseId":"00000000-0000-0000-0000-000000000001","databaseName":"system"}
{"time":"2025-09-22 08:20:22.085+0000","level":"INFO","category":"o.n.k.r.Recovery","message":"[system/00000000] Requirement `Database unavailable` makes database system unavailable.","databaseId":"00000000-0000-0000-0000-000000000001","databaseName":"system"}
{"time":"2025-09-22 08:20:22.085+0000","level":"INFO","category":"o.n.k.r.Recovery","message":"[system/00000000] DatabaseId{00000000[system]} is unavailable.","databaseId":"00000000-0000-0000-0000-000000000001","databaseName":"system"}
{"time":"2025-09-22 08:20:22.107+0000","level":"INFO","category":"o.n.k.d.Database","message":"[system/00000000] Transaction logs recovery is required with the last check point (which points to LogPosition{logVersion=0, byteOffset=3230}, oldest log entry to recover LogPosition{logVersion=0, byteOffset=3230}). First observed post checkpoint append index: 4.","databaseId":"00000000-0000-0000-0000-000000000001","databaseName":"system"}
{"time":"2025-09-22 08:20:22.108+0000","level":"INFO","category":"o.n.k.d.Database","message":"[system/00000000] Recovery required from position LogPosition{logVersion=0, byteOffset=3230}","databaseId":"00000000-0000-0000-0000-000000000001","databaseName":"system"}
{"time":"2025-09-22 08:20:22.191+0000","level":"INFO","category":"o.n.k.r.Recovery","message":"[system/00000000] TransactionLogsRecovery","databaseId":"00000000-0000-0000-0000-000000000001","databaseName":"system"}
{"time":"2025-09-22 08:20:22.233+0000","level":"INFO","category":"o.n.k.r.Recovery","message":"[system/00000000]  10% completed","databaseId":"00000000-0000-0000-0000-000000000001","databaseName":"system"}
{"time":"2025-09-22 08:20:22.239+0000","level":"INFO","category":"o.n.k.r.Recovery","message":"[system/00000000]  20% completed","databaseId":"00000000-0000-0000-0000-000000000001","databaseName":"system"}
{"time":"2025-09-22 08:20:22.244+0000","level":"INFO","category":"o.n.k.r.Recovery","message":"[system/00000000]  30% completed","databaseId":"00000000-0000-0000-0000-000000000001","databaseName":"system"}
{"time":"2025-09-22 08:20:22.253+0000","level":"INFO","category":"o.n.k.r.Recovery","message":"[system/00000000]  40% completed","databaseId":"00000000-0000-0000-0000-000000000001","databaseName":"system"}
{"time":"2025-09-22 08:20:22.257+0000","level":"INFO","category":"o.n.k.r.Recovery","message":"[system/00000000]  50% completed","databaseId":"00000000-0000-0000-0000-000000000001","databaseName":"system"}
{"time":"2025-09-22 08:20:22.316+0000","level":"INFO","category":"o.n.k.i.i.s.TokenIndexProvider","message":"[system/00000000] Schema index cleanup job registered: descriptor=Index( id=1, name='index_343aff4e', type='LOOKUP', schema=(:<any-labels>), indexProvider='token-lookup-1.0' ), indexFile=E:\\neo4j\\neo4j-community-2025.08.0-windows\\neo4j-community-2025.08.0\\data\\databases\\system\\schema\\index\\token-lookup-1.0\\1\\index-1","databaseId":"00000000-0000-0000-0000-000000000001","databaseName":"system"}
{"time":"2025-09-22 08:20:22.317+0000","level":"INFO","category":"o.n.k.i.i.s.TokenIndexProvider","message":"[system/00000000] Schema index cleanup job registered: descriptor=Index( id=1, name='index_343aff4e', type='LOOKUP', schema=(:<any-labels>), indexProvider='token-lookup-1.0' ), indexFile=E:\\neo4j\\neo4j-community-2025.08.0-windows\\neo4j-community-2025.08.0\\data\\databases\\system\\schema\\index\\token-lookup-1.0\\1\\index-1","databaseId":"00000000-0000-0000-0000-000000000001","databaseName":"system"}
{"time":"2025-09-22 08:20:22.318+0000","level":"INFO","category":"o.n.k.i.i.s.TokenIndexProvider","message":"[system/00000000] Schema index cleanup job started: descriptor=Index( id=1, name='index_343aff4e', type='LOOKUP', schema=(:<any-labels>), indexProvider='token-lookup-1.0' ), indexFile=E:\\neo4j\\neo4j-community-2025.08.0-windows\\neo4j-community-2025.08.0\\data\\databases\\system\\schema\\index\\token-lookup-1.0\\1\\index-1","databaseId":"00000000-0000-0000-0000-000000000001","databaseName":"system"}
{"time":"2025-09-22 08:20:22.319+0000","level":"INFO","category":"o.n.k.i.i.s.TokenIndexProvider","message":"[system/00000000] Schema index cleanup job started: descriptor=Index( id=1, name='index_343aff4e', type='LOOKUP', schema=(:<any-labels>), indexProvider='token-lookup-1.0' ), indexFile=E:\\neo4j\\neo4j-community-2025.08.0-windows\\neo4j-community-2025.08.0\\data\\databases\\system\\schema\\index\\token-lookup-1.0\\1\\index-1","databaseId":"00000000-0000-0000-0000-000000000001","databaseName":"system"}
{"time":"2025-09-22 08:20:22.327+0000","level":"INFO","category":"o.n.k.i.i.s.TokenIndexProvider","message":"[system/00000000] Schema index cleanup job finished: descriptor=Index( id=1, name='index_343aff4e', type='LOOKUP', schema=(:<any-labels>), indexProvider='token-lookup-1.0' ), indexFile=E:\\neo4j\\neo4j-community-2025.08.0-windows\\neo4j-community-2025.08.0\\data\\databases\\system\\schema\\index\\token-lookup-1.0\\1\\index-1 Number of pages visited: 2, Number of tree nodes: 1, Number of cleaned crashed pointers: 0, Time spent: 0ms","databaseId":"00000000-0000-0000-0000-000000000001","databaseName":"system"}
{"time":"2025-09-22 08:20:22.328+0000","level":"INFO","category":"o.n.k.i.i.s.TokenIndexProvider","message":"[system/00000000] Schema index cleanup job finished: descriptor=Index( id=1, name='index_343aff4e', type='LOOKUP', schema=(:<any-labels>), indexProvider='token-lookup-1.0' ), indexFile=E:\\neo4j\\neo4j-community-2025.08.0-windows\\neo4j-community-2025.08.0\\data\\databases\\system\\schema\\index\\token-lookup-1.0\\1\\index-1 Number of pages visited: 2, Number of tree nodes: 1, Number of cleaned crashed pointers: 0, Time spent: 0ms","databaseId":"00000000-0000-0000-0000-000000000001","databaseName":"system"}
{"time":"2025-09-22 08:20:22.329+0000","level":"INFO","category":"o.n.k.i.i.s.TokenIndexProvider","message":"[system/00000000] Schema index cleanup job closed: descriptor=Index( id=1, name='index_343aff4e', type='LOOKUP', schema=(:<any-labels>), indexProvider='token-lookup-1.0' ), indexFile=E:\\neo4j\\neo4j-community-2025.08.0-windows\\neo4j-community-2025.08.0\\data\\databases\\system\\schema\\index\\token-lookup-1.0\\1\\index-1","databaseId":"00000000-0000-0000-0000-000000000001","databaseName":"system"}
{"time":"2025-09-22 08:20:22.330+0000","level":"INFO","category":"o.n.k.i.i.s.TokenIndexProvider","message":"[system/00000000] Schema index cleanup job closed: descriptor=Index( id=1, name='index_343aff4e', type='LOOKUP', schema=(:<any-labels>), indexProvider='token-lookup-1.0' ), indexFile=E:\\neo4j\\neo4j-community-2025.08.0-windows\\neo4j-community-2025.08.0\\data\\databases\\system\\schema\\index\\token-lookup-1.0\\1\\index-1","databaseId":"00000000-0000-0000-0000-000000000001","databaseName":"system"}
{"time":"2025-09-22 08:20:22.333+0000","level":"INFO","category":"o.n.k.i.i.s.TokenIndexProvider","message":"[system/00000000] Schema index cleanup job registered: descriptor=Index( id=2, name='index_f7700477', type='LOOKUP', schema=()-[:<any-types>]-(), indexProvider='token-lookup-1.0' ), indexFile=E:\\neo4j\\neo4j-community-2025.08.0-windows\\neo4j-community-2025.08.0\\data\\databases\\system\\schema\\index\\token-lookup-1.0\\2\\index-2","databaseId":"00000000-0000-0000-0000-000000000001","databaseName":"system"}
{"time":"2025-09-22 08:20:22.334+0000","level":"INFO","category":"o.n.k.i.i.s.TokenIndexProvider","message":"[system/00000000] Schema index cleanup job registered: descriptor=Index( id=2, name='index_f7700477', type='LOOKUP', schema=()-[:<any-types>]-(), indexProvider='token-lookup-1.0' ), indexFile=E:\\neo4j\\neo4j-community-2025.08.0-windows\\neo4j-community-2025.08.0\\data\\databases\\system\\schema\\index\\token-lookup-1.0\\2\\index-2","databaseId":"00000000-0000-0000-0000-000000000001","databaseName":"system"}
{"time":"2025-09-22 08:20:22.334+0000","level":"INFO","category":"o.n.k.i.i.s.TokenIndexProvider","message":"[system/00000000] Schema index cleanup job started: descriptor=Index( id=2, name='index_f7700477', type='LOOKUP', schema=()-[:<any-types>]-(), indexProvider='token-lookup-1.0' ), indexFile=E:\\neo4j\\neo4j-community-2025.08.0-windows\\neo4j-community-2025.08.0\\data\\databases\\system\\schema\\index\\token-lookup-1.0\\2\\index-2","databaseId":"00000000-0000-0000-0000-000000000001","databaseName":"system"}
{"time":"2025-09-22 08:20:22.335+0000","level":"INFO","category":"o.n.k.i.i.s.TokenIndexProvider","message":"[system/00000000] Schema index cleanup job started: descriptor=Index( id=2, name='index_f7700477', type='LOOKUP', schema=()-[:<any-types>]-(), indexProvider='token-lookup-1.0' ), indexFile=E:\\neo4j\\neo4j-community-2025.08.0-windows\\neo4j-community-2025.08.0\\data\\databases\\system\\schema\\index\\token-lookup-1.0\\2\\index-2","databaseId":"00000000-0000-0000-0000-000000000001","databaseName":"system"}
{"time":"2025-09-22 08:20:22.335+0000","level":"INFO","category":"o.n.k.i.a.i.IndexingService","message":"[system/00000000] IndexingService.init: indexes not specifically mentioned above are ONLINE. Total 2 indexes. Processed in 52ms","databaseId":"00000000-0000-0000-0000-000000000001","databaseName":"system"}
{"time":"2025-09-22 08:20:22.335+0000","level":"INFO","category":"o.n.k.i.i.s.TokenIndexProvider","message":"[system/00000000] Schema index cleanup job finished: descriptor=Index( id=2, name='index_f7700477', type='LOOKUP', schema=()-[:<any-types>]-(), indexProvider='token-lookup-1.0' ), indexFile=E:\\neo4j\\neo4j-community-2025.08.0-windows\\neo4j-community-2025.08.0\\data\\databases\\system\\schema\\index\\token-lookup-1.0\\2\\index-2 Number of pages visited: 2, Number of tree nodes: 1, Number of cleaned crashed pointers: 0, Time spent: 0ms","databaseId":"00000000-0000-0000-0000-000000000001","databaseName":"system"}
{"time":"2025-09-22 08:20:22.336+0000","level":"INFO","category":"o.n.k.i.i.s.TokenIndexProvider","message":"[system/00000000] Schema index cleanup job finished: descriptor=Index( id=2, name='index_f7700477', type='LOOKUP', schema=()-[:<any-types>]-(), indexProvider='token-lookup-1.0' ), indexFile=E:\\neo4j\\neo4j-community-2025.08.0-windows\\neo4j-community-2025.08.0\\data\\databases\\system\\schema\\index\\token-lookup-1.0\\2\\index-2 Number of pages visited: 2, Number of tree nodes: 1, Number of cleaned crashed pointers: 0, Time spent: 0ms","databaseId":"00000000-0000-0000-0000-000000000001","databaseName":"system"}
{"time":"2025-09-22 08:20:22.336+0000","level":"INFO","category":"o.n.k.i.i.s.TokenIndexProvider","message":"[system/00000000] Schema index cleanup job closed: descriptor=Index( id=2, name='index_f7700477', type='LOOKUP', schema=()-[:<any-types>]-(), indexProvider='token-lookup-1.0' ), indexFile=E:\\neo4j\\neo4j-community-2025.08.0-windows\\neo4j-community-2025.08.0\\data\\databases\\system\\schema\\index\\token-lookup-1.0\\2\\index-2","databaseId":"00000000-0000-0000-0000-000000000001","databaseName":"system"}
{"time":"2025-09-22 08:20:22.336+0000","level":"INFO","category":"o.n.k.i.i.s.TokenIndexProvider","message":"[system/00000000] Schema index cleanup job closed: descriptor=Index( id=2, name='index_f7700477', type='LOOKUP', schema=()-[:<any-types>]-(), indexProvider='token-lookup-1.0' ), indexFile=E:\\neo4j\\neo4j-community-2025.08.0-windows\\neo4j-community-2025.08.0\\data\\databases\\system\\schema\\index\\token-lookup-1.0\\2\\index-2","databaseId":"00000000-0000-0000-0000-000000000001","databaseName":"system"}
{"time":"2025-09-22 08:20:22.395+0000","level":"INFO","category":"o.n.k.r.Recovery","message":"[system/00000000]  60% completed","databaseId":"00000000-0000-0000-0000-000000000001","databaseName":"system"}
{"time":"2025-09-22 08:20:22.407+0000","level":"INFO","category":"o.n.k.r.Recovery","message":"[system/00000000]  70% completed","databaseId":"00000000-0000-0000-0000-000000000001","databaseName":"system"}
{"time":"2025-09-22 08:20:22.432+0000","level":"INFO","category":"o.n.k.r.Recovery","message":"[system/00000000]  80% completed","databaseId":"00000000-0000-0000-0000-000000000001","databaseName":"system"}
{"time":"2025-09-22 08:20:22.452+0000","level":"INFO","category":"o.n.k.r.Recovery","message":"[system/00000000]  90% completed","databaseId":"00000000-0000-0000-0000-000000000001","databaseName":"system"}
{"time":"2025-09-22 08:20:22.461+0000","level":"INFO","category":"o.n.k.r.Recovery","message":"[system/00000000] 100% completed","databaseId":"00000000-0000-0000-0000-000000000001","databaseName":"system"}
{"time":"2025-09-22 08:20:22.467+0000","level":"INFO","category":"o.n.k.r.Recovery","message":"[system/00000000] Fulfilling of requirement 'Database unavailable' makes database system available.","databaseId":"00000000-0000-0000-0000-000000000001","databaseName":"system"}
{"time":"2025-09-22 08:20:22.467+0000","level":"INFO","category":"o.n.k.r.Recovery","message":"[system/00000000] DatabaseId{00000000[system]} is ready.","databaseId":"00000000-0000-0000-0000-000000000001","databaseName":"system"}
{"time":"2025-09-22 08:20:22.482+0000","level":"INFO","category":"o.n.k.i.a.i.IndexingService","message":"[system/00000000] IndexingService.start: index 1 on (:<any-labels>) is ONLINE","databaseId":"00000000-0000-0000-0000-000000000001","databaseName":"system"}
{"time":"2025-09-22 08:20:22.482+0000","level":"INFO","category":"o.n.k.i.a.i.IndexingService","message":"[system/00000000] IndexingService.start: index 2 on ()-[:<any-types>]-() is ONLINE","databaseId":"00000000-0000-0000-0000-000000000001","databaseName":"system"}
{"time":"2025-09-22 08:20:22.482+0000","level":"INFO","category":"o.n.k.i.a.i.IndexingService","message":"[system/00000000] IndexingService.start: indexes not specifically mentioned above are POPULATING. Total 8 indexes. Processed in 3ms","databaseId":"00000000-0000-0000-0000-000000000001","databaseName":"system"}
{"time":"2025-09-22 08:20:22.536+0000","level":"INFO","category":"o.n.k.i.a.i.IndexPopulationJob","message":"[system/00000000] Index population started: [Index( id=3, name='constraint_798238d6', type='RANGE', schema=(:DatabaseName {name, namespace}), indexProvider='range-1.0' )]","databaseId":"00000000-0000-0000-0000-000000000001","databaseName":"system"}
{"time":"2025-09-22 08:20:22.543+0000","level":"INFO","category":"o.n.k.i.t.l.c.CheckPointerImpl","message":"[system/00000000] Checkpoint triggered by \"Recovery completed.\" @ txId: 47, append index: 47 checkpoint started...","databaseId":"00000000-0000-0000-0000-000000000001","databaseName":"system"}
{"time":"2025-09-22 08:20:22.546+0000","level":"INFO","category":"o.n.k.i.a.i.IndexPopulationJob","message":"[system/00000000] Index population started: [Index( id=5, name='displayNameConstraint', type='RANGE', schema=(:DatabaseName {displayName}), indexProvider='range-1.0' )]","databaseId":"00000000-0000-0000-0000-000000000001","databaseName":"system"}
{"time":"2025-09-22 08:20:22.549+0000","level":"INFO","category":"o.n.k.i.a.i.IndexPopulationJob","message":"[system/00000000] Index population started: [Index( id=7, name='constraint_8014b60a', type='RANGE', schema=(:Database {name}), indexProvider='range-1.0' )]","databaseId":"00000000-0000-0000-0000-000000000001","databaseName":"system"}
{"time":"2025-09-22 08:20:22.552+0000","level":"INFO","category":"o.n.k.i.a.i.IndexPopulationJob","message":"[system/00000000] Index population started: [Index( id=9, name='constraint_5789ae3', type='RANGE', schema=(:User {name}), indexProvider='range-1.0' )]","databaseId":"00000000-0000-0000-0000-000000000001","databaseName":"system"}
{"time":"2025-09-22 08:20:22.556+0000","level":"INFO","category":"o.n.k.i.a.i.IndexPopulationJob","message":"[system/00000000] Index population started: [Index( id=11, name='constraint_74fad970', type='RANGE', schema=(:User {id}), indexProvider='range-1.0' )]","databaseId":"00000000-0000-0000-0000-000000000001","databaseName":"system"}
{"time":"2025-09-22 08:20:22.558+0000","level":"INFO","category":"o.n.k.i.a.i.IndexPopulationJob","message":"[system/00000000] Index population started: [Index( id=13, name='auth-constraint', type='RANGE', schema=(:Auth {provider, id}), indexProvider='range-1.0' )]","databaseId":"00000000-0000-0000-0000-000000000001","databaseName":"system"}
{"time":"2025-09-22 08:20:22.683+0000","level":"INFO","category":"o.n.k.i.t.l.c.CheckPointerImpl","message":"[system/00000000] Checkpoint triggered by \"Recovery completed.\" @ txId: 47, append index: 47 checkpoint completed in 138ms. Checkpoint flushed 68 pages (0% of total available pages), in 63 IOs. Checkpoint performed with IO limit: unlimited, paused in total 0 times(0 millis). Average checkpoint flush speed: 544.0KiB/s.","databaseId":"00000000-0000-0000-0000-000000000001","databaseName":"system"}
{"time":"2025-09-22 08:20:22.690+0000","level":"INFO","category":"o.n.k.i.t.l.p.LogPruningImpl","message":"[system/00000000] No log version pruned. The strategy used was '2 days ********** size'. ","databaseId":"00000000-0000-0000-0000-000000000001","databaseName":"system"}
{"time":"2025-09-22 08:20:22.693+0000","level":"INFO","category":"o.n.k.d.Database","message":"[system/00000000] Recovery in 'full' mode completed. Observed transactions range [first:4, last:47]: 44 transactions applied, 0 not completed transactions rolled back, skipped applying 0 previously rolled back transactions. Time spent: 991ms.","databaseId":"00000000-0000-0000-0000-000000000001","databaseName":"system"}
{"time":"2025-09-22 08:20:22.746+0000","level":"INFO","category":"o.n.k.r.Recovery","message":"[system/00000000] Requirement `Database unavailable` makes database system unavailable.","databaseId":"00000000-0000-0000-0000-000000000001","databaseName":"system"}
{"time":"2025-09-22 08:20:22.746+0000","level":"INFO","category":"o.n.k.r.Recovery","message":"[system/00000000] DatabaseId{00000000[system]} is unavailable.","databaseId":"00000000-0000-0000-0000-000000000001","databaseName":"system"}
{"time":"2025-09-22 08:20:22.848+0000","level":"INFO","category":"o.n.k.i.t.l.f.c.CheckpointLogFile","message":"[system/00000000] Scanning log file with version 0 for checkpoint entries","databaseId":"00000000-0000-0000-0000-000000000001","databaseName":"system"}
{"time":"2025-09-22 08:20:22.858+0000","level":"INFO","category":"o.n.k.d.Database","message":"[system/00000000] Current KernelVersion=KernelVersion{V2025_08,version=24}, LogFormat= V9","databaseId":"00000000-0000-0000-0000-000000000001","databaseName":"system"}
{"time":"2025-09-22 08:20:22.865+0000","level":"INFO","category":"o.n.k.i.s.f.RecordFormatSelector","message":"[system/00000000] Selected RecordFormat:PageAlignedV5_0[aligned-1.1] record format from store E:\\neo4j\\neo4j-community-2025.08.0-windows\\neo4j-community-2025.08.0\\data\\databases\\system","databaseId":"00000000-0000-0000-0000-000000000001","databaseName":"system"}
{"time":"2025-09-22 08:20:22.865+0000","level":"INFO","category":"o.n.k.i.s.f.RecordFormatSelector","message":"[system/00000000] Selected format from the store files: RecordFormat:PageAlignedV5_0[aligned-1.1]","databaseId":"00000000-0000-0000-0000-000000000001","databaseName":"system"}
{"time":"2025-09-22 08:20:23.185+0000","level":"INFO","category":"o.n.i.d.DiagnosticsManager","message":"[system/00000000] \r\n                                                                [system/00000000] ********************************************************************************\r\n                                                                [system/00000000]                               [ Database: system ]                              \r\n                                                                [system/00000000] ********************************************************************************\r\n                                                                [system/00000000] --------------------------------------------------------------------------------\r\n                                                                [system/00000000]                                   [ Version ]                                   \r\n                                                                [system/00000000] --------------------------------------------------------------------------------\r\n                                                                [system/00000000] DBMS: community record-aligned-1.1\r\n                                                                [system/00000000] Kernel version: 2025.08.0\r\n                                                                [system/00000000] \r\n                                                                [system/00000000] --------------------------------------------------------------------------------\r\n                                                                [system/00000000]                                 [ Store files ]                                 \r\n                                                                [system/00000000] --------------------------------------------------------------------------------\r\n                                                                [system/00000000] Disk space on partition (Total / Free / Free %): 512108785664 / 17338032128 / 3\r\n                                                                [system/00000000] Storage files stored on file store: NTFS\r\n                                                                [system/00000000] Storage files: (filename : modification date - size)\r\n                                                                [system/00000000]   database_lock: 2025-09-22 08:18:45.984+0000 - 0B\r\n                                                                [system/00000000]   id-buffer.tmp.0: 2025-09-22 08:18:45.976+0000 - 0B\r\n                                                                [system/00000000]   neostore: 2025-09-22 08:18:45.660+0000 - 8.000KiB\r\n                                                                [system/00000000]   neostore.counts.db: 2025-09-22 08:20:22.927+0000 - 48.00KiB\r\n                                                                [system/00000000]   neostore.indexstats.db: 2025-09-22 08:20:22.935+0000 - 40.00KiB\r\n                                                                [system/00000000]   neostore.labeltokenstore.db: 2025-09-22 08:20:22.668+0000 - 8.000KiB\r\n                                                                [system/00000000]   neostore.labeltokenstore.db.id: 2025-09-22 08:20:22.913+0000 - 40.00KiB\r\n                                                                [system/00000000]   neostore.labeltokenstore.db.names: 2025-09-22 08:20:22.639+0000 - 8.000KiB\r\n                                                                [system/00000000]   neostore.labeltokenstore.db.names.id: 2025-09-22 08:20:22.911+0000 - 40.00KiB\r\n                                                                [system/00000000]   neostore.nodestore.db: 2025-09-22 08:20:22.664+0000 - 8.000KiB\r\n                                                                [system/00000000]   neostore.nodestore.db.id: 2025-09-22 08:20:22.875+0000 - 40.00KiB\r\n                                                                [system/00000000]   neostore.nodestore.db.labels: 2025-09-22 08:18:45.608+0000 - 8.000KiB\r\n                                                                [system/00000000]   neostore.nodestore.db.labels.id: 2025-09-22 08:20:22.868+0000 - 40.00KiB\r\n                                                                [system/00000000]   neostore.propertystore.db: 2025-09-22 08:20:22.643+0000 - 8.000KiB\r\n                                                                [system/00000000]   neostore.propertystore.db.arrays: 2025-09-22 08:18:45.626+0000 - 8.000KiB\r\n                                                                [system/00000000]   neostore.propertystore.db.arrays.id: 2025-09-22 08:20:22.889+0000 - 40.00KiB\r\n                                                                [system/00000000]   neostore.propertystore.db.id: 2025-09-22 08:20:22.892+0000 - 40.00KiB\r\n                                                                [system/00000000]   neostore.propertystore.db.index: 2025-09-22 08:20:22.641+0000 - 8.000KiB\r\n                                                                [system/00000000]   neostore.propertystore.db.index.id: 2025-09-22 08:20:22.881+0000 - 40.00KiB\r\n                                                                [system/00000000]   neostore.propertystore.db.index.keys: 2025-09-22 08:20:22.659+0000 - 8.000KiB\r\n                                                                [system/00000000]   neostore.propertystore.db.index.keys.id: 2025-09-22 08:20:22.878+0000 - 40.00KiB\r\n                                                                [system/00000000]   neostore.propertystore.db.strings: 2025-09-22 08:20:22.656+0000 - 8.000KiB\r\n                                                                [system/00000000]   neostore.propertystore.db.strings.id: 2025-09-22 08:20:22.885+0000 - 40.00KiB\r\n                                                                [system/00000000]   neostore.relationshipgroupstore.db: 2025-09-22 08:18:45.650+0000 - 8.000KiB\r\n                                                                [system/00000000]   neostore.relationshipgroupstore.db.id: 2025-09-22 08:20:22.922+0000 - 40.00KiB\r\n                                                                [system/00000000]   neostore.relationshipgroupstore.degrees.db: 2025-09-22 08:20:22.930+0000 - 40.00KiB\r\n                                                                [system/00000000]   neostore.relationshipstore.db: 2025-09-22 08:20:22.667+0000 - 8.000KiB\r\n                                                                [system/00000000]   neostore.relationshipstore.db.id: 2025-09-22 08:20:22.899+0000 - 40.00KiB\r\n                                                                [system/00000000]   neostore.relationshiptypestore.db: 2025-09-22 08:20:22.676+0000 - 8.000KiB\r\n                                                                [system/00000000]   neostore.relationshiptypestore.db.id: 2025-09-22 08:20:22.907+0000 - 40.00KiB\r\n                                                                [system/00000000]   neostore.relationshiptypestore.db.names: 2025-09-22 08:20:22.640+0000 - 8.000KiB\r\n                                                                [system/00000000]   neostore.relationshiptypestore.db.names.id: 2025-09-22 08:20:22.903+0000 - 40.00KiB\r\n                                                                [system/00000000]   neostore.schemastore.db: 2025-09-22 08:20:22.648+0000 - 8.000KiB\r\n                                                                [system/00000000]   neostore.schemastore.db.id: 2025-09-22 08:20:22.919+0000 - 40.00KiB\r\n                                                                [system/00000000]   schema:\r\n                                                                [system/00000000]     index:\r\n                                                                [system/00000000]       range-1.0:\r\n                                                                [system/00000000]         11:\r\n                                                                [system/00000000]           index-11: 2025-09-22 08:20:22.674+0000 - 40.00KiB\r\n                                                                [system/00000000]         - Total: 2025-09-22 08:20:22.732+0000 - 40.00KiB\r\n                                                                [system/00000000]         13:\r\n                                                                [system/00000000]           index-13: 2025-09-22 08:20:22.652+0000 - 40.00KiB\r\n                                                                [system/00000000]         - Total: 2025-09-22 08:20:22.739+0000 - 40.00KiB\r\n                                                                [system/00000000]         3:\r\n                                                                [system/00000000]           index-3: 2025-09-22 08:20:22.670+0000 - 40.00KiB\r\n                                                                [system/00000000]         - Total: 2025-09-22 08:20:22.722+0000 - 40.00KiB\r\n                                                                [system/00000000]         5:\r\n                                                                [system/00000000]           index-5: 2025-09-22 08:20:22.650+0000 - 40.00KiB\r\n                                                                [system/00000000]         - Total: 2025-09-22 08:20:22.725+0000 - 40.00KiB\r\n                                                                [system/00000000]         7:\r\n                                                                [system/00000000]           index-7: 2025-09-22 08:20:22.646+0000 - 40.00KiB\r\n                                                                [system/00000000]         - Total: 2025-09-22 08:20:22.727+0000 - 40.00KiB\r\n                                                                [system/00000000]         9:\r\n                                                                [system/00000000]           index-9: 2025-09-22 08:20:22.663+0000 - 40.00KiB\r\n                                                                [system/00000000]         - Total: 2025-09-22 08:20:22.730+0000 - 40.00KiB\r\n                                                                [system/00000000]       - Total: 2025-09-22 08:20:22.560+0000 - 240.0KiB\r\n                                                                [system/00000000]       token-lookup-1.0:\r\n                                                                [system/00000000]         1:\r\n                                                                [system/00000000]           index-1: 2025-09-22 08:20:22.747+0000 - 48.00KiB\r\n                                                                [system/00000000]         - Total: 2025-09-22 08:18:46.478+0000 - 48.00KiB\r\n                                                                [system/00000000]         2:\r\n                                                                [system/00000000]           index-2: 2025-09-22 08:20:22.750+0000 - 48.00KiB\r\n                                                                [system/00000000]         - Total: 2025-09-22 08:18:46.385+0000 - 48.00KiB\r\n                                                                [system/00000000]       - Total: 2025-09-22 08:18:46.478+0000 - 96.00KiB\r\n                                                                [system/00000000]     - Total: 2025-09-22 08:18:46.800+0000 - 336.0KiB\r\n                                                                [system/00000000]   - Total: 2025-09-22 08:18:46.381+0000 - 336.0KiB\r\n                                                                [system/00000000] Storage summary: \r\n                                                                [system/00000000]   Total size of store: 1.117MiB\r\n                                                                [system/00000000]   Total size of mapped files: 1.117MiB\r\n                                                                [system/00000000] \r\n                                                                [system/00000000] --------------------------------------------------------------------------------\r\n                                                                [system/00000000]                               [ Transaction log ]                               \r\n                                                                [system/00000000] --------------------------------------------------------------------------------\r\n                                                                [system/00000000] Transaction log files stored on file store: NTFS\r\n                                                                [system/00000000] Transaction log metadata:\r\n                                                                [system/00000000]  - current kernel version used in transactions: V2025_08\r\n                                                                [system/00000000]  - last committed transaction id: 47\r\n                                                                [system/00000000] Transaction log files:\r\n                                                                [system/00000000]  - existing transaction log versions: 0-0\r\n                                                                [system/00000000]  - oldest append index 2 found in log with version 0\r\n                                                                [system/00000000]  - files: (filename : creation date - size)\r\n                                                                [system/00000000]      neostore.transaction.db.0: 2025-09-22 08:18:46.010+0000 - 26.59KiB\r\n                                                                [system/00000000]  - total size of files: 26.59KiB\r\n                                                                [system/00000000] Checkpoint log files:\r\n                                                                [system/00000000]  - existing checkpoint log versions: 0-0\r\n                                                                [system/00000000]  - last checkpoint: CheckpointInfo[oldestNotVisibleTransactionLogPosition=LogPosition{logVersion=0, byteOffset=27227}, transactionLogPosition=LogPosition{logVersion=0, byteOffset=27227}, storeId=StoreId{creationTime=1758529125653, random=-3618506131647564689, storageEngineName='record', formatName='aligned', majorVersion=1, minorVersion=1}, checkpointEntryPosition=LogPosition{logVersion=0, byteOffset=360}, channelPositionAfterCheckpoint=LogPosition{logVersion=0, byteOffset=592}, checkpointFilePostReadPosition=LogPosition{logVersion=0, byteOffset=592}, kernelVersion=KernelVersion{V2025_08,version=24}, kernelVersionByte=24, transactionId=TransactionId[id=47, appendIndex=47, kernelVersion=KernelVersion{V2025_08,version=24}, checksum=-1428365447, commitTimestamp=1758529127317, consensusIndex=-1], appendIndex=47, reason=Checkpoint triggered by \"Recovery completed.\" @ txId: 47, append index: 47, consensusIndexInCheckpoint=true]\r\n                                                                [system/00000000] \r\n                                                                [system/00000000] --------------------------------------------------------------------------------\r\n                                                                [system/00000000]                                   [ Id usage ]                                  \r\n                                                                [system/00000000] --------------------------------------------------------------------------------\r\n                                                                [system/00000000] ArrayPropertyStore[neostore.nodestore.db.labels]: used=1 high=0\r\n                                                                [system/00000000] NodeStore[neostore.nodestore.db]: used=7 high=6\r\n                                                                [system/00000000] StringPropertyStore[neostore.propertystore.db.index.keys]: used=48 high=47\r\n                                                                [system/00000000] PropertyIndexStore[neostore.propertystore.db.index]: used=34 high=33\r\n                                                                [system/00000000] StringPropertyStore[neostore.propertystore.db.strings]: used=3 high=2\r\n                                                                [system/00000000] ArrayPropertyStore[neostore.propertystore.db.arrays]: used=1 high=0\r\n                                                                [system/00000000] PropertyStore[neostore.propertystore.db]: used=85 high=84\r\n                                                                [system/00000000] RelationshipStore[neostore.relationshipstore.db]: used=3 high=2\r\n                                                                [system/00000000] StringPropertyStore[neostore.relationshiptypestore.db.names]: used=3 high=2\r\n                                                                [system/00000000] RelationshipTypeStore[neostore.relationshiptypestore.db]: used=2 high=1\r\n                                                                [system/00000000] StringPropertyStore[neostore.labeltokenstore.db.names]: used=6 high=5\r\n                                                                [system/00000000] LabelTokenStore[neostore.labeltokenstore.db]: used=5 high=4\r\n                                                                [system/00000000] SchemaStore[neostore.schemastore.db]: used=15 high=14\r\n                                                                [system/00000000] RelationshipGroupStore[neostore.relationshipgroupstore.db]: used=1 high=0\r\n                                                                [system/00000000] NeoStore[neostore]: used=-1 high=-1\r\n                                                                [system/00000000] \r\n                                                                [system/00000000] --------------------------------------------------------------------------------\r\n                                                                [system/00000000]                                   [ Metadata ]                                  \r\n                                                                [system/00000000] --------------------------------------------------------------------------------\r\n                                                                [system/00000000] EXTERNAL_STORE_UUID (Database identifier exposed as external store identity. Generated on creation and never updated): 05332ae1-329f-4a71-a6d1-************\r\n                                                                [system/00000000] DATABASE_ID (The last used DatabaseId for this database): null\r\n                                                                [system/00000000] LEGACY_STORE_VERSION (Legacy store format version. This field is used from 5.0 onwards only to distinguish non-migrated pre 5.0 metadata stores.): -3523014627327384477\r\n                                                                [system/00000000] STORE_ID (Store ID): StoreId{creationTime=1758529125653, random=-3618506131647564689, storageEngineName='record', formatName='aligned', majorVersion=1, minorVersion=1}\r\n                                                                [system/00000000] "}
{"time":"2025-09-22 08:20:23.217+0000","level":"WARN","category":"o.n.k.i.i.s.RangeIndexProvider","message":"[system/00000000] Failed to open index:3. Requesting re-population. Cause: Index is not fully initialized since its state pages are empty","databaseId":"00000000-0000-0000-0000-000000000001","databaseName":"system"}
{"time":"2025-09-22 08:20:23.220+0000","level":"WARN","category":"o.n.k.i.i.s.RangeIndexProvider","message":"[system/00000000] Failed to open index:5. Requesting re-population. Cause: Index is not fully initialized since its state pages are empty","databaseId":"00000000-0000-0000-0000-000000000001","databaseName":"system"}
{"time":"2025-09-22 08:20:23.222+0000","level":"WARN","category":"o.n.k.i.i.s.RangeIndexProvider","message":"[system/00000000] Failed to open index:7. Requesting re-population. Cause: Index is not fully initialized since its state pages are empty","databaseId":"00000000-0000-0000-0000-000000000001","databaseName":"system"}
{"time":"2025-09-22 08:20:23.224+0000","level":"WARN","category":"o.n.k.i.i.s.RangeIndexProvider","message":"[system/00000000] Failed to open index:9. Requesting re-population. Cause: Index is not fully initialized since its state pages are empty","databaseId":"00000000-0000-0000-0000-000000000001","databaseName":"system"}
{"time":"2025-09-22 08:20:23.227+0000","level":"WARN","category":"o.n.k.i.i.s.RangeIndexProvider","message":"[system/00000000] Failed to open index:11. Requesting re-population. Cause: Index is not fully initialized since its state pages are empty","databaseId":"00000000-0000-0000-0000-000000000001","databaseName":"system"}
{"time":"2025-09-22 08:20:23.231+0000","level":"WARN","category":"o.n.k.i.i.s.RangeIndexProvider","message":"[system/00000000] Failed to open index:13. Requesting re-population. Cause: Index is not fully initialized since its state pages are empty","databaseId":"00000000-0000-0000-0000-000000000001","databaseName":"system"}
{"time":"2025-09-22 08:20:23.232+0000","level":"INFO","category":"o.n.k.i.a.i.IndexingService","message":"[system/00000000] IndexingService.init: index 1 on (:<any-labels>) is ONLINE","databaseId":"00000000-0000-0000-0000-000000000001","databaseName":"system"}
{"time":"2025-09-22 08:20:23.233+0000","level":"INFO","category":"o.n.k.i.a.i.IndexingService","message":"[system/00000000] IndexingService.init: index 2 on ()-[:<any-types>]-() is ONLINE","databaseId":"00000000-0000-0000-0000-000000000001","databaseName":"system"}
{"time":"2025-09-22 08:20:23.233+0000","level":"INFO","category":"o.n.k.i.a.i.IndexingService","message":"[system/00000000] IndexingService.init: indexes not specifically mentioned above are POPULATING. Total 8 indexes. Processed in 30ms","databaseId":"00000000-0000-0000-0000-000000000001","databaseName":"system"}
{"time":"2025-09-22 08:20:23.240+0000","level":"INFO","category":"o.n.k.a.DatabaseAvailabilityGuard","message":"[system/00000000] Requirement `Database unavailable` makes database system unavailable.","databaseId":"00000000-0000-0000-0000-000000000001","databaseName":"system"}
{"time":"2025-09-22 08:20:23.240+0000","level":"INFO","category":"o.n.k.a.DatabaseAvailabilityGuard","message":"[system/00000000] DatabaseId{00000000[system]} is unavailable.","databaseId":"00000000-0000-0000-0000-000000000001","databaseName":"system"}
{"time":"2025-09-22 08:20:23.256+0000","level":"INFO","category":"o.n.k.d.Database","message":"[system/00000000] Starting transaction log [E:\\neo4j\\neo4j-community-2025.08.0-windows\\neo4j-community-2025.08.0\\data\\transactions\\system\\neostore.transaction.db.0] at version=0","databaseId":"00000000-0000-0000-0000-000000000001","databaseName":"system"}
{"time":"2025-09-22 08:20:23.264+0000","level":"INFO","category":"o.n.k.d.Database","message":"[system/00000000] Starting transaction log [E:\\neo4j\\neo4j-community-2025.08.0-windows\\neo4j-community-2025.08.0\\data\\transactions\\system\\checkpoint.0] at version=0","databaseId":"00000000-0000-0000-0000-000000000001","databaseName":"system"}
{"time":"2025-09-22 08:20:23.274+0000","level":"INFO","category":"o.n.k.i.t.l.f.c.CheckpointLogFile","message":"[system/00000000] Scanning log file with version 0 for checkpoint entries","databaseId":"00000000-0000-0000-0000-000000000001","databaseName":"system"}
{"time":"2025-09-22 08:20:23.283+0000","level":"INFO","category":"o.n.k.i.a.i.IndexingService","message":"[system/00000000] IndexingService.start: index 1 on (:<any-labels>) is ONLINE","databaseId":"00000000-0000-0000-0000-000000000001","databaseName":"system"}
{"time":"2025-09-22 08:20:23.285+0000","level":"INFO","category":"o.n.k.i.a.i.IndexingService","message":"[system/00000000] IndexingService.start: index 2 on ()-[:<any-types>]-() is ONLINE","databaseId":"00000000-0000-0000-0000-000000000001","databaseName":"system"}
{"time":"2025-09-22 08:20:23.285+0000","level":"INFO","category":"o.n.k.i.a.i.IndexingService","message":"[system/00000000] IndexingService.start: indexes not specifically mentioned above are POPULATING. Total 8 indexes. Processed in 1ms","databaseId":"00000000-0000-0000-0000-000000000001","databaseName":"system"}
{"time":"2025-09-22 08:20:23.296+0000","level":"INFO","category":"o.n.k.i.a.i.IndexPopulationJob","message":"[system/00000000] Index population started: [Index( id=3, name='constraint_798238d6', type='RANGE', schema=(:DatabaseName {name, namespace}), indexProvider='range-1.0', owningConstraint=4 )]","databaseId":"00000000-0000-0000-0000-000000000001","databaseName":"system"}
{"time":"2025-09-22 08:20:23.299+0000","level":"INFO","category":"o.n.k.i.a.i.IndexPopulationJob","message":"[system/00000000] Index population started: [Index( id=5, name='displayNameConstraint', type='RANGE', schema=(:DatabaseName {displayName}), indexProvider='range-1.0', owningConstraint=6 )]","databaseId":"00000000-0000-0000-0000-000000000001","databaseName":"system"}
{"time":"2025-09-22 08:20:23.301+0000","level":"INFO","category":"o.n.k.i.a.i.IndexPopulationJob","message":"[system/00000000] Index population started: [Index( id=7, name='constraint_8014b60a', type='RANGE', schema=(:Database {name}), indexProvider='range-1.0', owningConstraint=8 )]","databaseId":"00000000-0000-0000-0000-000000000001","databaseName":"system"}
{"time":"2025-09-22 08:20:23.304+0000","level":"INFO","category":"o.n.k.i.a.i.IndexPopulationJob","message":"[system/00000000] Index population started: [Index( id=9, name='constraint_5789ae3', type='RANGE', schema=(:User {name}), indexProvider='range-1.0', owningConstraint=10 )]","databaseId":"00000000-0000-0000-0000-000000000001","databaseName":"system"}
{"time":"2025-09-22 08:20:23.307+0000","level":"INFO","category":"o.n.k.i.a.i.IndexPopulationJob","message":"[system/00000000] Index population started: [Index( id=11, name='constraint_74fad970', type='RANGE', schema=(:User {id}), indexProvider='range-1.0', owningConstraint=12 )]","databaseId":"00000000-0000-0000-0000-000000000001","databaseName":"system"}
{"time":"2025-09-22 08:20:23.311+0000","level":"INFO","category":"o.n.k.i.a.i.IndexPopulationJob","message":"[system/00000000] Index population started: [Index( id=13, name='auth-constraint', type='RANGE', schema=(:Auth {provider, id}), indexProvider='range-1.0', owningConstraint=14 )]","databaseId":"00000000-0000-0000-0000-000000000001","databaseName":"system"}
{"time":"2025-09-22 08:20:23.402+0000","level":"INFO","category":"o.n.k.i.a.i.IndexPopulationJob","message":"[system/00000000] Index creation finished for index [Index( id=3, name='constraint_798238d6', type='RANGE', schema=(:DatabaseName {name, namespace}), indexProvider='range-1.0', owningConstraint=4 )].","databaseId":"00000000-0000-0000-0000-000000000001","databaseName":"system"}
{"time":"2025-09-22 08:20:23.429+0000","level":"INFO","category":"o.n.k.i.a.i.IndexPopulationJob","message":"[system/00000000] Index creation finished for index [Index( id=5, name='displayNameConstraint', type='RANGE', schema=(:DatabaseName {displayName}), indexProvider='range-1.0', owningConstraint=6 )].","databaseId":"00000000-0000-0000-0000-000000000001","databaseName":"system"}
{"time":"2025-09-22 08:20:23.457+0000","level":"INFO","category":"o.n.k.i.a.i.IndexPopulationJob","message":"[system/00000000] Index creation finished for index [Index( id=7, name='constraint_8014b60a', type='RANGE', schema=(:Database {name}), indexProvider='range-1.0', owningConstraint=8 )].","databaseId":"00000000-0000-0000-0000-000000000001","databaseName":"system"}
{"time":"2025-09-22 08:20:23.478+0000","level":"INFO","category":"o.n.k.i.a.i.IndexPopulationJob","message":"[system/00000000] Index creation finished for index [Index( id=9, name='constraint_5789ae3', type='RANGE', schema=(:User {name}), indexProvider='range-1.0', owningConstraint=10 )].","databaseId":"00000000-0000-0000-0000-000000000001","databaseName":"system"}
{"time":"2025-09-22 08:20:23.501+0000","level":"INFO","category":"o.n.k.i.a.i.IndexPopulationJob","message":"[system/00000000] Index creation finished for index [Index( id=11, name='constraint_74fad970', type='RANGE', schema=(:User {id}), indexProvider='range-1.0', owningConstraint=12 )].","databaseId":"00000000-0000-0000-0000-000000000001","databaseName":"system"}
{"time":"2025-09-22 08:20:23.522+0000","level":"INFO","category":"o.n.k.i.a.i.IndexPopulationJob","message":"[system/00000000] Index creation finished for index [Index( id=13, name='auth-constraint', type='RANGE', schema=(:Auth {provider, id}), indexProvider='range-1.0', owningConstraint=14 )].","databaseId":"00000000-0000-0000-0000-000000000001","databaseName":"system"}
{"time":"2025-09-22 08:20:23.529+0000","level":"INFO","category":"o.n.k.a.DatabaseAvailabilityGuard","message":"[system/00000000] Fulfilling of requirement 'Database unavailable' makes database system available.","databaseId":"00000000-0000-0000-0000-000000000001","databaseName":"system"}
{"time":"2025-09-22 08:20:23.530+0000","level":"INFO","category":"o.n.k.a.DatabaseAvailabilityGuard","message":"[system/00000000] DatabaseId{00000000[system]} is ready.","databaseId":"00000000-0000-0000-0000-000000000001","databaseName":"system"}
{"time":"2025-09-22 08:20:23.713+0000","level":"INFO","category":"o.n.u.UserDataCollector","message":"Anonymous Usage Data is being sent to Neo4j, see https://neo4j.com/docs/usage-data/"}
{"time":"2025-09-22 08:20:23.757+0000","level":"INFO","category":"o.n.s.s.s.UserSecurityGraphComponent","message":"Performing postInitialization step for component 'security-users' with version 5 and status CURRENT"}
{"time":"2025-09-22 08:20:23.757+0000","level":"INFO","category":"o.n.s.s.s.UserSecurityGraphComponent","message":"Updating the initial password in component 'security-users'"}
{"time":"2025-09-22 08:20:23.791+0000","level":"INFO","category":"o.n.d.d.DatabaseLifecycles","message":"Creating 'DatabaseId{03308fa4[neo4j]}'."}
{"time":"2025-09-22 08:20:23.800+0000","level":"INFO","category":"o.n.d.d.DatabaseLifecycles","message":"Starting 'DatabaseId{03308fa4[neo4j]}'."}
{"time":"2025-09-22 08:20:23.836+0000","level":"INFO","category":"o.n.k.i.t.l.f.c.CheckpointLogFile","message":"[neo4j/03308fa4] Scanning log file with version 0 for checkpoint entries","databaseId":"03308fa4-ae9d-4709-8321-4d70d0e956f8","databaseName":"neo4j"}
{"time":"2025-09-22 08:20:23.850+0000","level":"INFO","category":"o.n.k.d.Database","message":"[neo4j/03308fa4] Current KernelVersion=KernelVersion{V2025_08,version=24}, LogFormat= V9","databaseId":"03308fa4-ae9d-4709-8321-4d70d0e956f8","databaseName":"neo4j"}
{"time":"2025-09-22 08:20:23.854+0000","level":"INFO","category":"o.n.k.i.s.f.RecordFormatSelector","message":"[neo4j/03308fa4] Selected RecordFormat:PageAlignedV5_0[aligned-1.1] record format from store E:\\neo4j\\neo4j-community-2025.08.0-windows\\neo4j-community-2025.08.0\\data\\databases\\neo4j","databaseId":"03308fa4-ae9d-4709-8321-4d70d0e956f8","databaseName":"neo4j"}
{"time":"2025-09-22 08:20:23.854+0000","level":"INFO","category":"o.n.k.i.s.f.RecordFormatSelector","message":"[neo4j/03308fa4] Selected format from the store files: RecordFormat:PageAlignedV5_0[aligned-1.1]","databaseId":"03308fa4-ae9d-4709-8321-4d70d0e956f8","databaseName":"neo4j"}
{"time":"2025-09-22 08:20:23.980+0000","level":"INFO","category":"o.n.i.d.DiagnosticsManager","message":"[neo4j/03308fa4] \r\n                                                                [neo4j/03308fa4] ********************************************************************************\r\n                                                                [neo4j/03308fa4]                               [ Database: neo4j ]                               \r\n                                                                [neo4j/03308fa4] ********************************************************************************\r\n                                                                [neo4j/03308fa4] --------------------------------------------------------------------------------\r\n                                                                [neo4j/03308fa4]                                   [ Version ]                                   \r\n                                                                [neo4j/03308fa4] --------------------------------------------------------------------------------\r\n                                                                [neo4j/03308fa4] DBMS: community record-aligned-1.1\r\n                                                                [neo4j/03308fa4] Kernel version: 2025.08.0\r\n                                                                [neo4j/03308fa4] \r\n                                                                [neo4j/03308fa4] --------------------------------------------------------------------------------\r\n                                                                [neo4j/03308fa4]                                 [ Store files ]                                 \r\n                                                                [neo4j/03308fa4] --------------------------------------------------------------------------------\r\n                                                                [neo4j/03308fa4] Disk space on partition (Total / Free / Free %): 512108785664 / 17338032128 / 3\r\n                                                                [neo4j/03308fa4] Storage files stored on file store: NTFS\r\n                                                                [neo4j/03308fa4] Storage files: (filename : modification date - size)\r\n                                                                [neo4j/03308fa4]   database_lock: 2025-09-22 08:18:47.409+0000 - 0B\r\n                                                                [neo4j/03308fa4]   id-buffer.tmp.0: 2025-09-22 08:18:47.408+0000 - 0B\r\n                                                                [neo4j/03308fa4]   neostore: 2025-09-22 08:18:47.389+0000 - 8.000KiB\r\n                                                                [neo4j/03308fa4]   neostore.counts.db: 2025-09-22 08:20:23.943+0000 - 40.00KiB\r\n                                                                [neo4j/03308fa4]   neostore.indexstats.db: 2025-09-22 08:20:23.949+0000 - 40.00KiB\r\n                                                                [neo4j/03308fa4]   neostore.labeltokenstore.db: 2025-09-22 08:18:47.382+0000 - 0B\r\n                                                                [neo4j/03308fa4]   neostore.labeltokenstore.db.id: 2025-09-22 08:20:23.921+0000 - 40.00KiB\r\n                                                                [neo4j/03308fa4]   neostore.labeltokenstore.db.names: 2025-09-22 08:18:47.379+0000 - 8.000KiB\r\n                                                                [neo4j/03308fa4]   neostore.labeltokenstore.db.names.id: 2025-09-22 08:20:23.917+0000 - 40.00KiB\r\n                                                                [neo4j/03308fa4]   neostore.nodestore.db: 2025-09-22 08:18:47.353+0000 - 0B\r\n                                                                [neo4j/03308fa4]   neostore.nodestore.db.id: 2025-09-22 08:20:23.866+0000 - 40.00KiB\r\n                                                                [neo4j/03308fa4]   neostore.nodestore.db.labels: 2025-09-22 08:18:47.350+0000 - 8.000KiB\r\n                                                                [neo4j/03308fa4]   neostore.nodestore.db.labels.id: 2025-09-22 08:20:23.858+0000 - 40.00KiB\r\n                                                                [neo4j/03308fa4]   neostore.propertystore.db: 2025-09-22 08:18:47.564+0000 - 8.000KiB\r\n                                                                [neo4j/03308fa4]   neostore.propertystore.db.arrays: 2025-09-22 08:18:47.364+0000 - 8.000KiB\r\n                                                                [neo4j/03308fa4]   neostore.propertystore.db.arrays.id: 2025-09-22 08:20:23.887+0000 - 40.00KiB\r\n                                                                [neo4j/03308fa4]   neostore.propertystore.db.id: 2025-09-22 08:20:23.893+0000 - 40.00KiB\r\n                                                                [neo4j/03308fa4]   neostore.propertystore.db.index: 2025-09-22 08:18:47.562+0000 - 8.000KiB\r\n                                                                [neo4j/03308fa4]   neostore.propertystore.db.index.id: 2025-09-22 08:20:23.877+0000 - 40.00KiB\r\n                                                                [neo4j/03308fa4]   neostore.propertystore.db.index.keys: 2025-09-22 08:18:47.565+0000 - 8.000KiB\r\n                                                                [neo4j/03308fa4]   neostore.propertystore.db.index.keys.id: 2025-09-22 08:20:23.872+0000 - 40.00KiB\r\n                                                                [neo4j/03308fa4]   neostore.propertystore.db.strings: 2025-09-22 08:18:47.362+0000 - 8.000KiB\r\n                                                                [neo4j/03308fa4]   neostore.propertystore.db.strings.id: 2025-09-22 08:20:23.883+0000 - 40.00KiB\r\n                                                                [neo4j/03308fa4]   neostore.relationshipgroupstore.db: 2025-09-22 08:18:47.387+0000 - 8.000KiB\r\n                                                                [neo4j/03308fa4]   neostore.relationshipgroupstore.db.id: 2025-09-22 08:20:23.934+0000 - 40.00KiB\r\n                                                                [neo4j/03308fa4]   neostore.relationshipgroupstore.degrees.db: 2025-09-22 08:20:23.946+0000 - 40.00KiB\r\n                                                                [neo4j/03308fa4]   neostore.relationshipstore.db: 2025-09-22 08:18:47.371+0000 - 0B\r\n                                                                [neo4j/03308fa4]   neostore.relationshipstore.db.id: 2025-09-22 08:20:23.899+0000 - 40.00KiB\r\n                                                                [neo4j/03308fa4]   neostore.relationshiptypestore.db: 2025-09-22 08:18:47.377+0000 - 0B\r\n                                                                [neo4j/03308fa4]   neostore.relationshiptypestore.db.id: 2025-09-22 08:20:23.911+0000 - 40.00KiB\r\n                                                                [neo4j/03308fa4]   neostore.relationshiptypestore.db.names: 2025-09-22 08:18:47.372+0000 - 8.000KiB\r\n                                                                [neo4j/03308fa4]   neostore.relationshiptypestore.db.names.id: 2025-09-22 08:20:23.905+0000 - 40.00KiB\r\n                                                                [neo4j/03308fa4]   neostore.schemastore.db: 2025-09-22 08:18:47.567+0000 - 8.000KiB\r\n                                                                [neo4j/03308fa4]   neostore.schemastore.db.id: 2025-09-22 08:20:23.929+0000 - 40.00KiB\r\n                                                                [neo4j/03308fa4]   schema:\r\n                                                                [neo4j/03308fa4]     index:\r\n                                                                [neo4j/03308fa4]       token-lookup-1.0:\r\n                                                                [neo4j/03308fa4]         1:\r\n                                                                [neo4j/03308fa4]           index-1: 2025-09-22 08:18:47.487+0000 - 40.00KiB\r\n                                                                [neo4j/03308fa4]         - Total: 2025-09-22 08:18:47.466+0000 - 40.00KiB\r\n                                                                [neo4j/03308fa4]         2:\r\n                                                                [neo4j/03308fa4]           index-2: 2025-09-22 08:18:47.489+0000 - 40.00KiB\r\n                                                                [neo4j/03308fa4]         - Total: 2025-09-22 08:18:47.431+0000 - 40.00KiB\r\n                                                                [neo4j/03308fa4]       - Total: 2025-09-22 08:18:47.466+0000 - 80.00KiB\r\n                                                                [neo4j/03308fa4]     - Total: 2025-09-22 08:18:47.431+0000 - 80.00KiB\r\n                                                                [neo4j/03308fa4]   - Total: 2025-09-22 08:18:47.431+0000 - 80.00KiB\r\n                                                                [neo4j/03308fa4] Storage summary: \r\n                                                                [neo4j/03308fa4]   Total size of store: 848.0KiB\r\n                                                                [neo4j/03308fa4]   Total size of mapped files: 848.0KiB\r\n                                                                [neo4j/03308fa4] \r\n                                                                [neo4j/03308fa4] --------------------------------------------------------------------------------\r\n                                                                [neo4j/03308fa4]                               [ Transaction log ]                               \r\n                                                                [neo4j/03308fa4] --------------------------------------------------------------------------------\r\n                                                                [neo4j/03308fa4] Transaction log files stored on file store: NTFS\r\n                                                                [neo4j/03308fa4] Transaction log metadata:\r\n                                                                [neo4j/03308fa4]  - current kernel version used in transactions: V2025_08\r\n                                                                [neo4j/03308fa4]  - last committed transaction id: 3\r\n                                                                [neo4j/03308fa4] Transaction log files:\r\n                                                                [neo4j/03308fa4]  - existing transaction log versions: 0-0\r\n                                                                [neo4j/03308fa4]  - oldest append index 2 found in log with version 0\r\n                                                                [neo4j/03308fa4]  - files: (filename : creation date - size)\r\n                                                                [neo4j/03308fa4]      neostore.transaction.db.0: 2025-09-22 08:18:47.413+0000 - 3.154KiB\r\n                                                                [neo4j/03308fa4]  - total size of files: 3.154KiB\r\n                                                                [neo4j/03308fa4] Checkpoint log files:\r\n                                                                [neo4j/03308fa4]  - existing checkpoint log versions: 0-0\r\n                                                                [neo4j/03308fa4]  - last checkpoint: CheckpointInfo[oldestNotVisibleTransactionLogPosition=LogPosition{logVersion=0, byteOffset=3230}, transactionLogPosition=LogPosition{logVersion=0, byteOffset=3230}, storeId=StoreId{creationTime=1758529127389, random=2976458135536663148, storageEngineName='record', formatName='aligned', majorVersion=1, minorVersion=1}, checkpointEntryPosition=LogPosition{logVersion=0, byteOffset=128}, channelPositionAfterCheckpoint=LogPosition{logVersion=0, byteOffset=360}, checkpointFilePostReadPosition=LogPosition{logVersion=0, byteOffset=360}, kernelVersion=KernelVersion{V2025_08,version=24}, kernelVersionByte=24, transactionId=TransactionId[id=3, appendIndex=3, kernelVersion=KernelVersion{V2025_08,version=24}, checksum=-1369632451, commitTimestamp=1758529127428, consensusIndex=-1], appendIndex=3, reason=Checkpoint triggered by \"Database init completed.\" @ txId: 3, append index:, consensusIndexInCheckpoint=true]\r\n                                                                [neo4j/03308fa4] \r\n                                                                [neo4j/03308fa4] --------------------------------------------------------------------------------\r\n                                                                [neo4j/03308fa4]                                   [ Id usage ]                                  \r\n                                                                [neo4j/03308fa4] --------------------------------------------------------------------------------\r\n                                                                [neo4j/03308fa4] ArrayPropertyStore[neostore.nodestore.db.labels]: used=1 high=0\r\n                                                                [neo4j/03308fa4] NodeStore[neostore.nodestore.db]: used=0 high=-1\r\n                                                                [neo4j/03308fa4] StringPropertyStore[neostore.propertystore.db.index.keys]: used=20 high=19\r\n                                                                [neo4j/03308fa4] PropertyIndexStore[neostore.propertystore.db.index]: used=10 high=9\r\n                                                                [neo4j/03308fa4] StringPropertyStore[neostore.propertystore.db.strings]: used=1 high=0\r\n                                                                [neo4j/03308fa4] ArrayPropertyStore[neostore.propertystore.db.arrays]: used=1 high=0\r\n                                                                [neo4j/03308fa4] PropertyStore[neostore.propertystore.db]: used=8 high=7\r\n                                                                [neo4j/03308fa4] RelationshipStore[neostore.relationshipstore.db]: used=0 high=-1\r\n                                                                [neo4j/03308fa4] StringPropertyStore[neostore.relationshiptypestore.db.names]: used=1 high=0\r\n                                                                [neo4j/03308fa4] RelationshipTypeStore[neostore.relationshiptypestore.db]: used=0 high=-1\r\n                                                                [neo4j/03308fa4] StringPropertyStore[neostore.labeltokenstore.db.names]: used=1 high=0\r\n                                                                [neo4j/03308fa4] LabelTokenStore[neostore.labeltokenstore.db]: used=0 high=-1\r\n                                                                [neo4j/03308fa4] SchemaStore[neostore.schemastore.db]: used=3 high=2\r\n                                                                [neo4j/03308fa4] RelationshipGroupStore[neostore.relationshipgroupstore.db]: used=1 high=0\r\n                                                                [neo4j/03308fa4] NeoStore[neostore]: used=-1 high=-1\r\n                                                                [neo4j/03308fa4] \r\n                                                                [neo4j/03308fa4] --------------------------------------------------------------------------------\r\n                                                                [neo4j/03308fa4]                                   [ Metadata ]                                  \r\n                                                                [neo4j/03308fa4] --------------------------------------------------------------------------------\r\n                                                                [neo4j/03308fa4] EXTERNAL_STORE_UUID (Database identifier exposed as external store identity. Generated on creation and never updated): 9aa8d521-762b-4e40-9d80-9bc7d39e9d8e\r\n                                                                [neo4j/03308fa4] DATABASE_ID (The last used DatabaseId for this database): null\r\n                                                                [neo4j/03308fa4] LEGACY_STORE_VERSION (Legacy store format version. This field is used from 5.0 onwards only to distinguish non-migrated pre 5.0 metadata stores.): -3523014627327384477\r\n                                                                [neo4j/03308fa4] STORE_ID (Store ID): StoreId{creationTime=1758529127389, random=2976458135536663148, storageEngineName='record', formatName='aligned', majorVersion=1, minorVersion=1}\r\n                                                                [neo4j/03308fa4] "}
{"time":"2025-09-22 08:20:23.988+0000","level":"INFO","category":"o.n.k.i.i.s.TokenIndexProvider","message":"[neo4j/03308fa4] Schema index cleanup job registered: descriptor=Index( id=1, name='index_343aff4e', type='LOOKUP', schema=(:<any-labels>), indexProvider='token-lookup-1.0' ), indexFile=E:\\neo4j\\neo4j-community-2025.08.0-windows\\neo4j-community-2025.08.0\\data\\databases\\neo4j\\schema\\index\\token-lookup-1.0\\1\\index-1","databaseId":"03308fa4-ae9d-4709-8321-4d70d0e956f8","databaseName":"neo4j"}
{"time":"2025-09-22 08:20:23.989+0000","level":"INFO","category":"o.n.k.i.i.s.TokenIndexProvider","message":"[neo4j/03308fa4] Schema index cleanup job started: descriptor=Index( id=1, name='index_343aff4e', type='LOOKUP', schema=(:<any-labels>), indexProvider='token-lookup-1.0' ), indexFile=E:\\neo4j\\neo4j-community-2025.08.0-windows\\neo4j-community-2025.08.0\\data\\databases\\neo4j\\schema\\index\\token-lookup-1.0\\1\\index-1","databaseId":"03308fa4-ae9d-4709-8321-4d70d0e956f8","databaseName":"neo4j"}
{"time":"2025-09-22 08:20:23.990+0000","level":"INFO","category":"o.n.k.i.i.s.TokenIndexProvider","message":"[neo4j/03308fa4] Schema index cleanup job finished: descriptor=Index( id=1, name='index_343aff4e', type='LOOKUP', schema=(:<any-labels>), indexProvider='token-lookup-1.0' ), indexFile=E:\\neo4j\\neo4j-community-2025.08.0-windows\\neo4j-community-2025.08.0\\data\\databases\\neo4j\\schema\\index\\token-lookup-1.0\\1\\index-1 Number of pages visited: 2, Number of tree nodes: 1, Number of cleaned crashed pointers: 0, Time spent: 0ms","databaseId":"03308fa4-ae9d-4709-8321-4d70d0e956f8","databaseName":"neo4j"}
{"time":"2025-09-22 08:20:23.990+0000","level":"INFO","category":"o.n.k.i.i.s.TokenIndexProvider","message":"[neo4j/03308fa4] Schema index cleanup job closed: descriptor=Index( id=1, name='index_343aff4e', type='LOOKUP', schema=(:<any-labels>), indexProvider='token-lookup-1.0' ), indexFile=E:\\neo4j\\neo4j-community-2025.08.0-windows\\neo4j-community-2025.08.0\\data\\databases\\neo4j\\schema\\index\\token-lookup-1.0\\1\\index-1","databaseId":"03308fa4-ae9d-4709-8321-4d70d0e956f8","databaseName":"neo4j"}
{"time":"2025-09-22 08:20:23.994+0000","level":"INFO","category":"o.n.k.i.i.s.TokenIndexProvider","message":"[neo4j/03308fa4] Schema index cleanup job registered: descriptor=Index( id=2, name='index_f7700477', type='LOOKUP', schema=()-[:<any-types>]-(), indexProvider='token-lookup-1.0' ), indexFile=E:\\neo4j\\neo4j-community-2025.08.0-windows\\neo4j-community-2025.08.0\\data\\databases\\neo4j\\schema\\index\\token-lookup-1.0\\2\\index-2","databaseId":"03308fa4-ae9d-4709-8321-4d70d0e956f8","databaseName":"neo4j"}
{"time":"2025-09-22 08:20:23.994+0000","level":"INFO","category":"o.n.k.i.a.i.IndexingService","message":"[neo4j/03308fa4] IndexingService.init: indexes not specifically mentioned above are ONLINE. Total 2 indexes. Processed in 11ms","databaseId":"03308fa4-ae9d-4709-8321-4d70d0e956f8","databaseName":"neo4j"}
{"time":"2025-09-22 08:20:23.995+0000","level":"INFO","category":"o.n.k.i.i.s.TokenIndexProvider","message":"[neo4j/03308fa4] Schema index cleanup job started: descriptor=Index( id=2, name='index_f7700477', type='LOOKUP', schema=()-[:<any-types>]-(), indexProvider='token-lookup-1.0' ), indexFile=E:\\neo4j\\neo4j-community-2025.08.0-windows\\neo4j-community-2025.08.0\\data\\databases\\neo4j\\schema\\index\\token-lookup-1.0\\2\\index-2","databaseId":"03308fa4-ae9d-4709-8321-4d70d0e956f8","databaseName":"neo4j"}
{"time":"2025-09-22 08:20:23.996+0000","level":"INFO","category":"o.n.k.i.i.s.TokenIndexProvider","message":"[neo4j/03308fa4] Schema index cleanup job finished: descriptor=Index( id=2, name='index_f7700477', type='LOOKUP', schema=()-[:<any-types>]-(), indexProvider='token-lookup-1.0' ), indexFile=E:\\neo4j\\neo4j-community-2025.08.0-windows\\neo4j-community-2025.08.0\\data\\databases\\neo4j\\schema\\index\\token-lookup-1.0\\2\\index-2 Number of pages visited: 2, Number of tree nodes: 1, Number of cleaned crashed pointers: 0, Time spent: 0ms","databaseId":"03308fa4-ae9d-4709-8321-4d70d0e956f8","databaseName":"neo4j"}
{"time":"2025-09-22 08:20:23.997+0000","level":"INFO","category":"o.n.k.i.i.s.TokenIndexProvider","message":"[neo4j/03308fa4] Schema index cleanup job closed: descriptor=Index( id=2, name='index_f7700477', type='LOOKUP', schema=()-[:<any-types>]-(), indexProvider='token-lookup-1.0' ), indexFile=E:\\neo4j\\neo4j-community-2025.08.0-windows\\neo4j-community-2025.08.0\\data\\databases\\neo4j\\schema\\index\\token-lookup-1.0\\2\\index-2","databaseId":"03308fa4-ae9d-4709-8321-4d70d0e956f8","databaseName":"neo4j"}
{"time":"2025-09-22 08:20:23.999+0000","level":"INFO","category":"o.n.k.a.DatabaseAvailabilityGuard","message":"[neo4j/03308fa4] Requirement `Database unavailable` makes database neo4j unavailable.","databaseId":"03308fa4-ae9d-4709-8321-4d70d0e956f8","databaseName":"neo4j"}
{"time":"2025-09-22 08:20:23.999+0000","level":"INFO","category":"o.n.k.a.DatabaseAvailabilityGuard","message":"[neo4j/03308fa4] DatabaseId{03308fa4[neo4j]} is unavailable.","databaseId":"03308fa4-ae9d-4709-8321-4d70d0e956f8","databaseName":"neo4j"}
{"time":"2025-09-22 08:20:24.003+0000","level":"INFO","category":"o.n.k.d.Database","message":"[neo4j/03308fa4] Starting transaction log [E:\\neo4j\\neo4j-community-2025.08.0-windows\\neo4j-community-2025.08.0\\data\\transactions\\neo4j\\neostore.transaction.db.0] at version=0","databaseId":"03308fa4-ae9d-4709-8321-4d70d0e956f8","databaseName":"neo4j"}
{"time":"2025-09-22 08:20:24.007+0000","level":"INFO","category":"o.n.k.d.Database","message":"[neo4j/03308fa4] Starting transaction log [E:\\neo4j\\neo4j-community-2025.08.0-windows\\neo4j-community-2025.08.0\\data\\transactions\\neo4j\\checkpoint.0] at version=0","databaseId":"03308fa4-ae9d-4709-8321-4d70d0e956f8","databaseName":"neo4j"}
{"time":"2025-09-22 08:20:24.009+0000","level":"INFO","category":"o.n.k.i.t.l.f.c.CheckpointLogFile","message":"[neo4j/03308fa4] Scanning log file with version 0 for checkpoint entries","databaseId":"03308fa4-ae9d-4709-8321-4d70d0e956f8","databaseName":"neo4j"}
{"time":"2025-09-22 08:20:24.016+0000","level":"INFO","category":"o.n.k.i.a.i.IndexingService","message":"[neo4j/03308fa4] IndexingService.start: indexes not specifically mentioned above are ONLINE. Total 2 indexes. Processed in 0ms","databaseId":"03308fa4-ae9d-4709-8321-4d70d0e956f8","databaseName":"neo4j"}
{"time":"2025-09-22 08:20:24.019+0000","level":"INFO","category":"o.n.k.a.DatabaseAvailabilityGuard","message":"[neo4j/03308fa4] Fulfilling of requirement 'Database unavailable' makes database neo4j available.","databaseId":"03308fa4-ae9d-4709-8321-4d70d0e956f8","databaseName":"neo4j"}
{"time":"2025-09-22 08:20:24.020+0000","level":"INFO","category":"o.n.k.a.DatabaseAvailabilityGuard","message":"[neo4j/03308fa4] DatabaseId{03308fa4[neo4j]} is ready.","databaseId":"03308fa4-ae9d-4709-8321-4d70d0e956f8","databaseName":"neo4j"}
{"time":"2025-09-22 08:20:24.157+0000","level":"INFO","category":"o.n.b.p.c.c.n.SocketNettyConnector","message":"Bolt enabled on localhost:7687."}
{"time":"2025-09-22 08:20:24.162+0000","level":"WARN","category":"i.n.b.ServerBootstrap","message":"Unknown channel option 'SO_REUSEADDR' for channel '[id: 0xe6f65d4f]'"}
{"time":"2025-09-22 08:20:24.177+0000","level":"INFO","category":"o.n.b.BoltServer","message":"Bolt server started"}
{"time":"2025-09-22 08:20:24.177+0000","level":"INFO","category":"o.n.s.A.ServerComponentsLifecycleAdapter","message":"Starting web server"}
{"time":"2025-09-22 08:20:25.639+0000","level":"INFO","category":"o.n.s.CommunityNeoWebServer","message":"HTTP enabled on localhost:7474."}
{"time":"2025-09-22 08:20:25.639+0000","level":"INFO","category":"o.n.s.CommunityNeoWebServer","message":"Remote interface available at http://localhost:7474/"}
{"time":"2025-09-22 08:20:25.644+0000","level":"INFO","category":"o.n.s.A.ServerComponentsLifecycleAdapter","message":"Web server started."}
{"time":"2025-09-22 08:20:25.649+0000","level":"INFO","category":"o.n.g.f.DatabaseManagementServiceFactory","message":"id: 0218C4F89E5A67861172F8AC28DC672FE65E55995A506B20293349DA824C4F54"}
{"time":"2025-09-22 08:20:25.650+0000","level":"INFO","category":"o.n.g.f.DatabaseManagementServiceFactory","message":"name: system"}
{"time":"2025-09-22 08:20:25.652+0000","level":"INFO","category":"o.n.g.f.DatabaseManagementServiceFactory","message":"creationDate: 2025-09-22T08:18:45.653Z"}
{"time":"2025-09-22 08:37:04.234+0000","level":"INFO","category":"o.n.k.i.t.l.c.CheckPointerImpl","message":"[system/00000000] Checkpoint triggered by \"Scheduled checkpoint for every 15 minutes threshold\" @ txId: 48, append index: 48 checkpoint started...","databaseId":"00000000-0000-0000-0000-000000000001","databaseName":"system"}
{"time":"2025-09-22 08:37:04.320+0000","level":"INFO","category":"o.n.k.i.t.l.c.CheckPointerImpl","message":"[system/00000000] Checkpoint triggered by \"Scheduled checkpoint for every 15 minutes threshold\" @ txId: 48, append index: 48 checkpoint completed in 82ms. Checkpoint flushed 33 pages (0% of total available pages), in 33 IOs. Checkpoint performed with IO limit: unlimited, paused in total 0 times(0 millis). Average checkpoint flush speed: 264.0KiB/s.","databaseId":"00000000-0000-0000-0000-000000000001","databaseName":"system"}
{"time":"2025-09-22 08:37:04.320+0000","level":"INFO","category":"o.n.k.i.t.l.p.LogPruningImpl","message":"[system/00000000] No log version pruned. The strategy used was '2 days ********** size'. ","databaseId":"00000000-0000-0000-0000-000000000001","databaseName":"system"}
{"time":"2025-09-22 11:01:53.427+0000","level":"WARN","category":"o.n.b.p.c.c.c.AtomicSchedulingConnection","message":"[bolt-56] The client is unauthorized due to authentication failure."}
{"time":"2025-09-22 11:02:04.040+0000","level":"WARN","category":"o.n.b.p.c.c.c.AtomicSchedulingConnection","message":"[bolt-57] The client is unauthorized due to authentication failure."}
{"time":"2025-09-22 11:02:05.337+0000","level":"WARN","category":"o.n.b.p.c.c.c.AtomicSchedulingConnection","message":"[bolt-58] The client is unauthorized due to authentication failure."}
{"time":"2025-09-22 11:02:06.510+0000","level":"WARN","category":"o.n.b.p.c.c.c.AtomicSchedulingConnection","message":"[bolt-59] The client has provided incorrect authentication details too many times in a row."}
{"time":"2025-09-22 11:02:07.695+0000","level":"WARN","category":"o.n.b.p.c.c.c.AtomicSchedulingConnection","message":"[bolt-60] Unsupported authentication token, missing key `credentials`"}
{"time":"2025-09-22 12:30:14.542+0000","level":"INFO","category":"o.n.g.f.m.GlobalModule","message":"Logging config in use: File 'E:\\neo4j\\neo4j-community-2025.08.0-windows\\neo4j-community-2025.08.0\\conf\\server-logs.xml'"}
{"time":"2025-09-22 12:30:14.691+0000","level":"WARN","category":"o.n.k.i.JvmChecker","message":"The max heap memory has not been configured. It is recommended that it is always explicitly configured, to ensure the system has a balanced configuration. Until then, a JVM computed heuristic of 4261412864 bytes is used instead. If you are running neo4j server, you need to configure server.memory.heap.max_size in neo4j.conf. If you are running neo4j embedded, you have to launch the JVM with -Xmx set to a value. You can run neo4j-admin server memory-recommendation for memory configuration suggestions."}
{"time":"2025-09-22 12:30:14.696+0000","level":"WARN","category":"o.n.k.i.JvmChecker","message":"The initial heap memory has not been configured. It is recommended that it is always explicitly configured, to ensure the system has a balanced configuration. Until then, a JVM computed heuristic of 266338304 bytes is used instead. If you are running neo4j server, you need to configure server.memory.heap.initial_size in neo4j.conf. If you are running neo4j embedded, you have to launch the JVM with -Xms set to a value. You can run neo4j-admin server memory-recommendation for memory configuration suggestions."}
{"time":"2025-09-22 12:30:14.851+0000","level":"WARN","category":"o.n.i.p.PageCache","message":"The server.memory.pagecache.size setting has not been configured. It is recommended that this setting is always explicitly configured, to ensure the system has a balanced configuration. Until then, a computed heuristic value of 6390712320 bytes will be used instead. Run `neo4j-admin memory-recommendation` for memory configuration suggestions."}
{"time":"2025-09-22 12:30:14.993+0000","level":"INFO","category":"o.n.d.i.DefaultIdentityModule","message":"Found ServerId on disk: ServerId{b35704b1} (b35704b1-c3a8-499a-ba0c-4ffa7fac8f09)"}
{"time":"2025-09-22 12:30:14.995+0000","level":"INFO","category":"o.n.d.i.DefaultIdentityModule","message":"This instance is ServerId{b35704b1} (b35704b1-c3a8-499a-ba0c-4ffa7fac8f09)"}
{"time":"2025-09-22 12:30:16.881+0000","level":"INFO","category":"o.n.s.CommunityNeoWebServer","message":"======== Neo4j 2025.08.0 ========"}
{"time":"2025-09-22 12:30:17.067+0000","level":"INFO","category":"o.n.d.d.DatabaseLifecycles","message":"Creating 'DatabaseId{00000000[system]}'."}
{"time":"2025-09-22 12:30:17.068+0000","level":"INFO","category":"o.n.i.d.DiagnosticsManager","message":"\r\n                                                                ********************************************************************************\r\n                                                                                             [ System diagnostics ]                             \r\n                                                                ********************************************************************************"}
{"time":"2025-09-22 12:30:17.068+0000","level":"INFO","category":"o.n.i.d.DiagnosticsManager","message":"\r\n                                                                --------------------------------------------------------------------------------\r\n                                                                                         [ System memory information ]                          \r\n                                                                --------------------------------------------------------------------------------\r\n                                                                Total Physical memory: 15.87GiB\r\n                                                                Free Physical memory: 5.635GiB\r\n                                                                Committed virtual memory: 327.4MiB\r\n                                                                Total swap space: 30.87GiB\r\n                                                                Free swap space: 8.253GiB\r\n                                                                "}
{"time":"2025-09-22 12:30:17.068+0000","level":"INFO","category":"o.n.i.d.DiagnosticsManager","message":"\r\n                                                                --------------------------------------------------------------------------------\r\n                                                                                           [ JVM memory information ]                           \r\n                                                                --------------------------------------------------------------------------------\r\n                                                                Free  memory: 53.27MiB\r\n                                                                Total memory: 94.00MiB\r\n                                                                Max   memory: 3.969GiB\r\n                                                                Garbage Collector: G1 Young Generation: [G1 Eden Space, G1 Survivor Space, G1 Old Gen]\r\n                                                                Garbage Collector: G1 Concurrent GC: [G1 Old Gen]\r\n                                                                Garbage Collector: G1 Old Generation: [G1 Eden Space, G1 Survivor Space, G1 Old Gen]\r\n                                                                Memory Pool: CodeHeap 'non-nmethods' (Non-heap memory): committed=3.313MiB, used=3.268MiB, max=7.313MiB, threshold=0B\r\n                                                                Memory Pool: Metaspace (Non-heap memory): committed=49.06MiB, used=48.40MiB, max=-1B, threshold=0B\r\n                                                                Memory Pool: CodeHeap 'profiled nmethods' (Non-heap memory): committed=8.750MiB, used=8.591MiB, max=116.3MiB, threshold=0B\r\n                                                                Memory Pool: Compressed Class Space (Non-heap memory): committed=6.188MiB, used=5.918MiB, max=1.000GiB, threshold=0B\r\n                                                                Memory Pool: G1 Eden Space (Heap memory): committed=52.00MiB, used=8.000MiB, max=-1B, threshold=?\r\n                                                                Memory Pool: G1 Old Gen (Heap memory): committed=40.00MiB, used=30.00MiB, max=3.969GiB, threshold=0B\r\n                                                                Memory Pool: G1 Survivor Space (Heap memory): committed=2.000MiB, used=1.413MiB, max=-1B, threshold=?\r\n                                                                Memory Pool: CodeHeap 'non-profiled nmethods' (Non-heap memory): committed=2.563MiB, used=2.553MiB, max=116.4MiB, threshold=0B\r\n                                                                "}
{"time":"2025-09-22 12:30:17.069+0000","level":"INFO","category":"o.n.i.d.DiagnosticsManager","message":"\r\n                                                                --------------------------------------------------------------------------------\r\n                                                                                        [ Operating system information ]                        \r\n                                                                --------------------------------------------------------------------------------\r\n                                                                Operating System: Windows 10; version: 10.0; arch: amd64; cpus: 16\r\n                                                                Process id: 28692\r\n                                                                Byte order: LITTLE_ENDIAN\r\n                                                                Local timezone: Asia/Shanghai\r\n                                                                Memory page size: 4096\r\n                                                                Unaligned memory access allowed: true\r\n                                                                "}
{"time":"2025-09-22 12:30:17.069+0000","level":"INFO","category":"o.n.i.d.DiagnosticsManager","message":"\r\n                                                                --------------------------------------------------------------------------------\r\n                                                                                              [ JVM information ]                               \r\n                                                                --------------------------------------------------------------------------------\r\n                                                                VM Name: OpenJDK 64-Bit Server VM\r\n                                                                VM Vendor: Eclipse Adoptium\r\n                                                                VM Version: 21.0.8+9-LTS\r\n                                                                JIT compiler: HotSpot 64-Bit Tiered Compilers\r\n                                                                VM Arguments: [-XX:+UseG1GC, -XX:-OmitStackTraceInFastThrow, -XX:+AlwaysPreTouch, -XX:+UnlockExperimentalVMOptions, -XX:+TrustFinalNonStaticFields, -XX:+DisableExplicitGC, -Djdk.nio.maxCachedBufferSize=1024, -Dio.netty.tryReflectionSetAccessible=true, -Dio.netty.leakDetection.level=DISABLED, -Djdk.tls.ephemeralDHKeySize=2048, -Djdk.tls.rejectClientInitiatedRenegotiation=true, -XX:FlightRecorderOptions=stackdepth=256, -XX:+UnlockDiagnosticVMOptions, -XX:+DebugNonSafepoints, --add-opens=java.base/java.nio=ALL-UNNAMED, --add-opens=java.base/java.io=ALL-UNNAMED, --add-opens=java.base/sun.nio.ch=ALL-UNNAMED, --add-opens=java.base/java.util.concurrent=ALL-UNNAMED, --enable-native-access=ALL-UNNAMED, -Dlog4j2.disable.jmx=true, -Dlog4j.layout.jsonTemplate.maxStringLength=32768, -Dfile.encoding=UTF-8]\r\n                                                                "}
{"time":"2025-09-22 12:30:17.069+0000","level":"INFO","category":"o.n.i.d.DiagnosticsManager","message":"\r\n\r\n                                                                --------------------------------------------------------------------------------\r\n                                                                                               [ Java classpath ]                               \r\n                                                                --------------------------------------------------------------------------------\r\n                                                                 [classpath] E:\\neo4j\\neo4j-community-2025.08.0-windows\\neo4j-community-2025.08.0\\lib\\scala-reflect-2.13.16.jar\r\n                                                                 [classpath] E:\\neo4j\\neo4j-community-2025.08.0-windows\\neo4j-community-2025.08.0\\lib\\neo4j-consistency-check-2025.08.0.jar\r\n                                                                 [classpath] E:\\neo4j\\neo4j-community-2025.08.0-windows\\neo4j-community-2025.08.0\\lib\\listenablefuture-9999.0-empty-to-avoid-conflict-with-guava.jar\r\n                                                                 [classpath] E:\\neo4j\\neo4j-community-2025.08.0-windows\\neo4j-community-2025.08.0\\lib\\neo4j-java-driver-6.0.0-alpha03.jar\r\n                                                                 [classpath] E:\\neo4j\\neo4j-community-2025.08.0-windows\\neo4j-community-2025.08.0\\lib\\jctools-core-4.0.5.jar\r\n                                                                 [classpath] E:\\neo4j\\neo4j-community-2025.08.0-windows\\neo4j-community-2025.08.0\\lib\\guava-33.4.8-jre.jar\r\n                                                                 [classpath] E:\\neo4j\\neo4j-community-2025.08.0-windows\\neo4j-community-2025.08.0\\lib\\neo4j-cypher-cache-2025.08.0.jar\r\n                                                                 [classpath] E:\\neo4j\\neo4j-community-2025.08.0-windows\\neo4j-community-2025.08.0\\lib\\grpc-protobuf-1.73.0.jar\r\n                                                                 [classpath] E:\\neo4j\\neo4j-community-2025.08.0-windows\\neo4j-community-2025.08.0\\lib\\neo4j-diagnostics-2025.08.0.jar\r\n                                                                 [classpath] E:\\neo4j\\neo4j-community-2025.08.0-windows\\neo4j-community-2025.08.0\\lib\\neo4j-expressions-2025.08.0.jar\r\n                                                                 [classpath] E:\\neo4j\\neo4j-community-2025.08.0-windows\\neo4j-community-2025.08.0\\lib\\neo4j-collections-2025.08.0.jar\r\n                                                                 [classpath] E:\\neo4j\\neo4j-community-2025.08.0-windows\\neo4j-community-2025.08.0\\lib\\neo4j-ast-2025.08.0.jar\r\n                                                                 [classpath] E:\\neo4j\\neo4j-community-2025.08.0-windows\\neo4j-community-2025.08.0\\lib\\cypher-parser-ast-common-2025.08.0.jar\r\n                                                                 [classpath] E:\\neo4j\\neo4j-community-2025.08.0-windows\\neo4j-community-2025.08.0\\lib\\stax-ex-1.8.1.jar\r\n                                                                 [classpath] E:\\neo4j\\neo4j-community-2025.08.0-windows\\neo4j-community-2025.08.0\\lib\\jsr305-3.0.2.jar\r\n                                                                 [classpath] E:\\neo4j\\neo4j-community-2025.08.0-windows\\neo4j-community-2025.08.0\\lib\\neo4j-codegen-2025.08.0.jar\r\n                                                                 [classpath] E:\\neo4j\\neo4j-community-2025.08.0-windows\\neo4j-community-2025.08.0\\lib\\neo4j-exceptions-2025.08.0.jar\r\n                                                                 [classpath] E:\\neo4j\\neo4j-community-2025.08.0-windows\\neo4j-community-2025.08.0\\lib\\neo4j-capabilities-2025.08.0.jar\r\n                                                                 [classpath] E:\\neo4j\\neo4j-community-2025.08.0-windows\\neo4j-community-2025.08.0\\lib\\jetty-http2-hpack-12.0.17.jar\r\n                                                                 [classpath] E:\\neo4j\\neo4j-community-2025.08.0-windows\\neo4j-community-2025.08.0\\lib\\neo4j-data-collector-2025.08.0.jar\r\n                                                                 [classpath] E:\\neo4j\\neo4j-community-2025.08.0-windows\\neo4j-community-2025.08.0\\lib\\reactive-streams-1.0.4.jar\r\n                                                                 [classpath] E:\\neo4j\\neo4j-community-2025.08.0-windows\\neo4j-community-2025.08.0\\lib\\jaxb-api-2.2.12.jar\r\n                                                                 [classpath] E:\\neo4j\\neo4j-community-2025.08.0-windows\\neo4j-community-2025.08.0\\lib\\arrow-vector-18.3.0.jar\r\n                                                                 [classpath] E:\\neo4j\\neo4j-community-2025.08.0-windows\\neo4j-community-2025.08.0\\lib\\jetty-ee8-nested-12.0.17.jar\r\n                                                                 [classpath] E:\\neo4j\\neo4j-community-2025.08.0-windows\\neo4j-community-2025.08.0\\lib\\jakarta.activation-api-1.2.2.jar\r\n                                                                 [classpath] E:\\neo4j\\neo4j-community-2025.08.0-windows\\neo4j-community-2025.08.0\\lib\\jansi-2.4.0.jar\r\n                                                                 [classpath] E:\\neo4j\\neo4j-community-2025.08.0-windows\\neo4j-community-2025.08.0\\lib\\neo4j-util-2025.08.0.jar\r\n                                                                 [classpath] E:\\neo4j\\neo4j-community-2025.08.0-windows\\neo4j-community-2025.08.0\\lib\\neo4j-fabric-2025.08.0.jar\r\n                                                                 [classpath] E:\\neo4j\\neo4j-community-2025.08.0-windows\\neo4j-community-2025.08.0\\lib\\jakarta.annotation-api-1.3.5.jar\r\n                                                                 [classpath] E:\\neo4j\\neo4j-community-2025.08.0-windows\\neo4j-community-2025.08.0\\lib\\parquet-encoding-1.15.2.jar\r\n                                                                 [classpath] E:\\neo4j\\neo4j-community-2025.08.0-windows\\neo4j-community-2025.08.0\\lib\\jna-5.17.0.jar\r\n                                                                 [classpath] E:\\neo4j\\neo4j-community-2025.08.0-windows\\neo4j-community-2025.08.0\\lib\\jersey-server-2.43.jar\r\n                                                                 [classpath] E:\\neo4j\\neo4j-community-2025.08.0-windows\\neo4j-community-2025.08.0\\lib\\jetty-http2-server-12.0.17.jar\r\n                                                                 [classpath] E:\\neo4j\\neo4j-community-2025.08.0-windows\\neo4j-community-2025.08.0\\lib\\txw2-2.3.2.jar\r\n                                                                 [classpath] E:\\neo4j\\neo4j-community-2025.08.0-windows\\neo4j-community-2025.08.0\\lib\\shiro-lang-2.0.5.jar\r\n                                                                 [classpath] E:\\neo4j\\neo4j-community-2025.08.0-windows\\neo4j-community-2025.08.0\\lib\\neo4j-layout-2025.08.0.jar\r\n                                                                 [classpath] E:\\neo4j\\neo4j-community-2025.08.0-windows\\neo4j-community-2025.08.0\\lib\\log4j-api-2.20.0.jar\r\n                                                                 [classpath] E:\\neo4j\\neo4j-community-2025.08.0-windows\\neo4j-community-2025.08.0\\lib\\jackson-jaxrs-base-2.19.1.jar\r\n                                                                 [classpath] E:\\neo4j\\neo4j-community-2025.08.0-windows\\neo4j-community-2025.08.0\\lib\\jakarta.validation-api-2.0.2.jar\r\n                                                                 [classpath] E:\\neo4j\\neo4j-community-2025.08.0-windows\\neo4j-community-2025.08.0\\lib\\grpc-netty-1.73.0.jar\r\n                                                                 [classpath] E:\\neo4j\\neo4j-community-2025.08.0-windows\\neo4j-community-2025.08.0\\lib\\flight-core-18.3.0.jar\r\n                                                                 [classpath] E:\\neo4j\\neo4j-community-2025.08.0-windows\\neo4j-community-2025.08.0\\lib\\neo4j-native-2025.08.0.jar\r\n                                                                 [classpath] E:\\neo4j\\neo4j-community-2025.08.0-windows\\neo4j-community-2025.08.0\\lib\\commons-configuration2-2.12.0.jar\r\n                                                                 [classpath] E:\\neo4j\\neo4j-community-2025.08.0-windows\\neo4j-community-2025.08.0\\lib\\neo4j-record-storage-engine-2025.08.0.jar\r\n                                                                 [classpath] E:\\neo4j\\neo4j-community-2025.08.0-windows\\neo4j-community-2025.08.0\\lib\\neo4j-bolt-2025.08.0.jar\r\n                                                                 [classpath] E:\\neo4j\\neo4j-community-2025.08.0-windows\\neo4j-community-2025.08.0\\lib\\neo4j-2025.08.0.jar"}
{"time":"2025-09-22 12:30:17.071+0000","level":"INFO","category":"o.n.i.d.DiagnosticsManager","message":"\r\n                                                                 [classpath] E:\\neo4j\\neo4j-community-2025.08.0-windows\\neo4j-community-2025.08.0\\lib\\zstd-jni-1.5.7-4.jar\r\n                                                                 [classpath] E:\\neo4j\\neo4j-community-2025.08.0-windows\\neo4j-community-2025.08.0\\lib\\neo4j-kernel-2025.08.0.jar\r\n                                                                 [classpath] E:\\neo4j\\neo4j-community-2025.08.0-windows\\neo4j-community-2025.08.0\\lib\\asm-util-9.8.jar\r\n                                                                 [classpath] E:\\neo4j\\neo4j-community-2025.08.0-windows\\neo4j-community-2025.08.0\\lib\\neo4j-lucene-index-2025.08.0.jar\r\n                                                                 [classpath] E:\\neo4j\\neo4j-community-2025.08.0-windows\\neo4j-community-2025.08.0\\lib\\neo4j-bolt-connection-routed-6.0.2.jar\r\n                                                                 [classpath] E:\\neo4j\\neo4j-community-2025.08.0-windows\\neo4j-community-2025.08.0\\lib\\jPowerShell-3.0.jar\r\n                                                                 [classpath] E:\\neo4j\\neo4j-community-2025.08.0-windows\\neo4j-community-2025.08.0\\lib\\magnolia_2.13-1.1.8.jar\r\n                                                                 [classpath] E:\\neo4j\\neo4j-community-2025.08.0-windows\\neo4j-community-2025.08.0\\lib\\antlr4-runtime-4.13.2.jar\r\n                                                                 [classpath] E:\\neo4j\\neo4j-community-2025.08.0-windows\\neo4j-community-2025.08.0\\lib\\jline-terminal-jansi-3.21.0.jar\r\n                                                                 [classpath] E:\\neo4j\\neo4j-community-2025.08.0-windows\\neo4j-community-2025.08.0\\lib\\arrow-memory-netty-buffer-patch-18.3.0.jar\r\n                                                                 [classpath] E:\\neo4j\\neo4j-community-2025.08.0-windows\\neo4j-community-2025.08.0\\lib\\jersey-client-2.43.jar\r\n                                                                 [classpath] E:\\neo4j\\neo4j-community-2025.08.0-windows\\neo4j-community-2025.08.0\\lib\\neo4j-unsafe-2025.08.0.jar\r\n                                                                 [classpath] E:\\neo4j\\neo4j-community-2025.08.0-windows\\neo4j-community-2025.08.0\\lib\\neo4j-cypher-dsl-2024.7.1.jar\r\n                                                                 [classpath] E:\\neo4j\\neo4j-community-2025.08.0-windows\\neo4j-community-2025.08.0\\lib\\asm-tree-9.8.jar\r\n                                                                 [classpath] E:\\neo4j\\neo4j-community-2025.08.0-windows\\neo4j-community-2025.08.0\\lib\\netty-transport-native-epoll-4.2.4.Final-linux-aarch_64.jar\r\n                                                                 [classpath] E:\\neo4j\\neo4j-community-2025.08.0-windows\\neo4j-community-2025.08.0\\lib\\parquet-hadoop-1.15.2.jar\r\n                                                                 [classpath] E:\\neo4j\\neo4j-community-2025.08.0-windows\\neo4j-community-2025.08.0\\lib\\shiro-event-2.0.5.jar\r\n                                                                 [classpath] E:\\neo4j\\neo4j-community-2025.08.0-windows\\neo4j-community-2025.08.0\\lib\\neo4j-cypher-slotted-runtime-2025.08.0.jar\r\n                                                                 [classpath] E:\\neo4j\\neo4j-community-2025.08.0-windows\\neo4j-community-2025.08.0\\lib\\neo4j-cypher-planner-2025.08.0.jar\r\n                                                                 [classpath] E:\\neo4j\\neo4j-community-2025.08.0-windows\\neo4j-community-2025.08.0\\lib\\cypher-v5-ast-factory-2025.08.0.jar\r\n                                                                 [classpath] E:\\neo4j\\neo4j-community-2025.08.0-windows\\neo4j-community-2025.08.0\\lib\\cypher-v25-parser-listener-2025.08.0.jar\r\n                                                                 [classpath] E:\\neo4j\\neo4j-community-2025.08.0-windows\\neo4j-community-2025.08.0\\lib\\parquet-floor-1.57.jar\r\n                                                                 [classpath] E:\\neo4j\\neo4j-community-2025.08.0-windows\\neo4j-community-2025.08.0\\lib\\neo4j-dbms-2025.08.0.jar\r\n                                                                 [classpath] E:\\neo4j\\neo4j-community-2025.08.0-windows\\neo4j-community-2025.08.0\\lib\\jersey-container-servlet-2.43.jar\r\n                                                                 [classpath] E:\\neo4j\\neo4j-community-2025.08.0-windows\\neo4j-community-2025.08.0\\lib\\jackson-databind-2.19.1.jar\r\n                                                                 [classpath] E:\\neo4j\\neo4j-community-2025.08.0-windows\\neo4j-community-2025.08.0\\lib\\parquet-jackson-1.15.2.jar\r\n                                                                 [classpath] E:\\neo4j\\neo4j-community-2025.08.0-windows\\neo4j-community-2025.08.0\\lib\\jetty-ee8-webapp-12.0.17.jar\r\n                                                                 [classpath] E:\\neo4j\\neo4j-community-2025.08.0-windows\\neo4j-community-2025.08.0\\lib\\parquet-column-1.15.2.jar\r\n                                                                 [classpath] E:\\neo4j\\neo4j-community-2025.08.0-windows\\neo4j-community-2025.08.0\\lib\\neo4j-cypher-config-2025.08.0.jar\r\n                                                                 [classpath] E:\\neo4j\\neo4j-community-2025.08.0-windows\\neo4j-community-2025.08.0\\lib\\netty-transport-native-kqueue-4.2.4.Final-osx-aarch_64.jar\r\n                                                                 [classpath] E:\\neo4j\\neo4j-community-2025.08.0-windows\\neo4j-community-2025.08.0\\lib\\cypher-v25-ast-factory-2025.08.0.jar\r\n                                                                 [classpath] E:\\neo4j\\neo4j-community-2025.08.0-windows\\neo4j-community-2025.08.0\\lib\\neo4j-procedure-api-2025.08.0.jar\r\n                                                                 [classpath] E:\\neo4j\\neo4j-community-2025.08.0-windows\\neo4j-community-2025.08.0\\lib\\netty-codec-http2-4.2.4.Final.jar\r\n                                                                 [classpath] E:\\neo4j\\neo4j-community-2025.08.0-windows\\neo4j-community-2025.08.0\\conf\\*\r\n                                                                 [classpath] E:\\neo4j\\neo4j-community-2025.08.0-windows\\neo4j-community-2025.08.0\\lib\\arrow-format-18.3.0.jar\r\n                                                                 [classpath] E:\\neo4j\\neo4j-community-2025.08.0-windows\\neo4j-community-2025.08.0\\lib\\neo4j-token-api-2025.08.0.jar\r\n                                                                 [classpath] E:\\neo4j\\neo4j-community-2025.08.0-windows\\neo4j-community-2025.08.0\\lib\\neo4j-command-line-2025.08.0.jar\r\n                                                                 [classpath] E:\\neo4j\\neo4j-community-2025.08.0-windows\\neo4j-community-2025.08.0\\lib\\cypher-antlr-common-2025.08.0.jar\r\n                                                                 [classpath] E:\\neo4j\\neo4j-community-2025.08.0-windows\\neo4j-community-2025.08.0\\lib\\commons-logging-1.3.5.jar\r\n                                                                 [classpath] E:\\neo4j\\neo4j-community-2025.08.0-windows\\neo4j-community-2025.08.0\\lib\\neo4j-front-end-2025.08.0.jar\r\n                                                                 [classpath] E:\\neo4j\\neo4j-community-2025.08.0-windows\\neo4j-community-2025.08.0\\lib\\cypher-parser-common-2025.08.0.jar\r\n                                                                 [classpath] E:\\neo4j\\neo4j-community-2025.08.0-windows\\neo4j-community-2025.08.0\\lib\\neo4j-udc-2025.08.0.jar\r\n                                                                 [classpath] E:\\neo4j\\neo4j-community-2025.08.0-windows\\neo4j-community-2025.08.0\\lib\\grpc-util-1.73.0.jar\r\n                                                                 [classpath] E:\\neo4j\\neo4j-community-2025.08.0-windows\\neo4j-community-2025.08.0\\lib\\neo4j-cypher-planner-spi-2025.08.0.jar\r\n                                                                 [classpath] E:\\neo4j\\neo4j-community-2025.08.0-windows\\neo4j-community-2025.08.0\\lib\\jersey-hk2-2.43.jar\r\n                                                                 [classpath] E:\\neo4j\\neo4j-community-2025.08.0-windows\\neo4j-community-2025.08.0\\lib\\shiro-crypto-cipher-2.0.5.jar\r\n                                                                 [classpath] E:\\neo4j\\neo4j-community-2025.08.0-windows\\neo4j-community-2025.08.0\\lib\\jackson-datatype-jsr310-2.18.3.jar\r\n                                                                 [classpath] E:\\neo4j\\neo4j-community-2025.08.0-windows\\neo4j-community-2025.08.0\\lib\\neo4j-cypher-2025.08.0.jar\r\n                                                                 [classpath] E:\\neo4j\\neo4j-community-2025.08.0-windows\\neo4j-community-2025.08.0\\lib\\neo4j-spatial-index-2025.08.0.jar\r\n                                                                 [classpath] E:\\neo4j\\neo4j-community-2025.08.0-windows\\neo4j-community-2025.08.0\\lib\\jetty-xml-12.0.17.jar"}
{"time":"2025-09-22 12:30:17.073+0000","level":"INFO","category":"o.n.i.d.DiagnosticsManager","message":"\r\n                                                                 [classpath] E:\\neo4j\\neo4j-community-2025.08.0-windows\\neo4j-community-2025.08.0\\lib\\neo4j-bolt-connection-netty-6.0.2.jar\r\n                                                                 [classpath] E:\\neo4j\\neo4j-community-2025.08.0-windows\\neo4j-community-2025.08.0\\lib\\neo4j-values-2025.08.0.jar\r\n                                                                 [classpath] E:\\neo4j\\neo4j-community-2025.08.0-windows\\neo4j-community-2025.08.0\\lib\\netty-buffer-4.2.4.Final.jar\r\n                                                                 [classpath] E:\\neo4j\\neo4j-community-2025.08.0-windows\\neo4j-community-2025.08.0\\lib\\cypher-preparser-2025.08.0.jar\r\n                                                                 [classpath] E:\\neo4j\\neo4j-community-2025.08.0-windows\\neo4j-community-2025.08.0\\lib\\annotations-2025.08.0.jar\r\n                                                                 [classpath] E:\\neo4j\\neo4j-community-2025.08.0-windows\\neo4j-community-2025.08.0\\lib\\jetty-server-12.0.17.jar\r\n                                                                 [classpath] E:\\neo4j\\neo4j-community-2025.08.0-windows\\neo4j-community-2025.08.0\\lib\\jaxb-runtime-2.3.2.jar\r\n                                                                 [classpath] E:\\neo4j\\neo4j-community-2025.08.0-windows\\neo4j-community-2025.08.0\\lib\\neo4j-notifications-2025.08.0.jar\r\n                                                                 [classpath] E:\\neo4j\\neo4j-community-2025.08.0-windows\\neo4j-community-2025.08.0\\lib\\lucene-backward-codecs-9.11.1.jar\r\n                                                                 [classpath] E:\\neo4j\\neo4j-community-2025.08.0-windows\\neo4j-community-2025.08.0\\lib\\WMI4Java-1.6.3.jar\r\n                                                                 [classpath] E:\\neo4j\\neo4j-community-2025.08.0-windows\\neo4j-community-2025.08.0\\lib\\slf4j-api-2.0.17.jar\r\n                                                                 [classpath] E:\\neo4j\\neo4j-community-2025.08.0-windows\\neo4j-community-2025.08.0\\lib\\netty-transport-native-epoll-4.2.4.Final-linux-x86_64.jar\r\n                                                                 [classpath] E:\\neo4j\\neo4j-community-2025.08.0-windows\\neo4j-community-2025.08.0\\lib\\jackson-annotations-2.19.1.jar\r\n                                                                 [classpath] E:\\neo4j\\neo4j-community-2025.08.0-windows\\neo4j-community-2025.08.0\\lib\\hk2-locator-2.6.1.jar\r\n                                                                 [classpath] E:\\neo4j\\neo4j-community-2025.08.0-windows\\neo4j-community-2025.08.0\\lib\\neo4j-indexcommands-2025.08.0.jar\r\n                                                                 [classpath] E:\\neo4j\\neo4j-community-2025.08.0-windows\\neo4j-community-2025.08.0\\lib\\cypher-v5-parser-listener-2025.08.0.jar\r\n                                                                 [classpath] E:\\neo4j\\neo4j-community-2025.08.0-windows\\neo4j-community-2025.08.0\\lib\\jackson-jaxrs-json-provider-2.19.1.jar\r\n                                                                 [classpath] E:\\neo4j\\neo4j-community-2025.08.0-windows\\neo4j-community-2025.08.0\\lib\\reactor-core-3.7.7.jar\r\n                                                                 [classpath] E:\\neo4j\\neo4j-community-2025.08.0-windows\\neo4j-community-2025.08.0\\lib\\proto-google-common-protos-2.51.0.jar\r\n                                                                 [classpath] E:\\neo4j\\neo4j-community-2025.08.0-windows\\neo4j-community-2025.08.0\\lib\\caffeine-3.2.1.jar\r\n                                                                 [classpath] E:\\neo4j\\neo4j-community-2025.08.0-windows\\neo4j-community-2025.08.0\\lib\\netty-transport-native-unix-common-4.2.4.Final.jar\r\n                                                                 [classpath] E:\\neo4j\\neo4j-community-2025.08.0-windows\\neo4j-community-2025.08.0\\lib\\netty-transport-native-kqueue-4.2.4.Final-osx-x86_64.jar\r\n                                                                 [classpath] E:\\neo4j\\neo4j-community-2025.08.0-windows\\neo4j-community-2025.08.0\\lib\\neo4j-cypher-physical-planning-2025.08.0.jar\r\n                                                                 [classpath] E:\\neo4j\\neo4j-community-2025.08.0-windows\\neo4j-community-2025.08.0\\lib\\shiro-config-core-2.0.5.jar\r\n                                                                 [classpath] E:\\neo4j\\neo4j-community-2025.08.0-windows\\neo4j-community-2025.08.0\\lib\\jakarta.xml.bind-api-2.3.2.jar\r\n                                                                 [classpath] E:\\neo4j\\neo4j-community-2025.08.0-windows\\neo4j-community-2025.08.0\\lib\\neo4j-cypher-logical-plans-2025.08.0.jar\r\n                                                                 [classpath] E:\\neo4j\\neo4j-community-2025.08.0-windows\\neo4j-community-2025.08.0\\lib\\scala-collection-contrib_2.13-0.3.0.jar\r\n                                                                 [classpath] E:\\neo4j\\neo4j-community-2025.08.0-windows\\neo4j-community-2025.08.0\\lib\\cypher-shell-2025.08.0.jar\r\n                                                                 [classpath] E:\\neo4j\\neo4j-community-2025.08.0-windows\\neo4j-community-2025.08.0\\lib\\grpc-core-1.73.0.jar\r\n                                                                 [classpath] E:\\neo4j\\neo4j-community-2025.08.0-windows\\neo4j-community-2025.08.0\\lib\\eclipse-collections-11.1.0.jar\r\n                                                                 [classpath] E:\\neo4j\\neo4j-community-2025.08.0-windows\\neo4j-community-2025.08.0\\lib\\netty-codec-base-4.2.4.Final.jar\r\n                                                                 [classpath] E:\\neo4j\\neo4j-community-2025.08.0-windows\\neo4j-community-2025.08.0\\lib\\javax.annotation-api-1.3.2.jar\r\n                                                                 [classpath] E:\\neo4j\\neo4j-community-2025.08.0-windows\\neo4j-community-2025.08.0\\lib\\neo4j-rewriting-2025.08.0.jar\r\n                                                                 [classpath] E:\\neo4j\\neo4j-community-2025.08.0-windows\\neo4j-community-2025.08.0\\lib\\javassist-3.30.2-GA.jar\r\n                                                                 [classpath] E:\\neo4j\\neo4j-community-2025.08.0-windows\\neo4j-community-2025.08.0\\lib\\commons-compress-1.27.1.jar\r\n                                                                 [classpath] E:\\neo4j\\neo4j-community-2025.08.0-windows\\neo4j-community-2025.08.0\\lib\\neo4j-bootcheck-2025.08.0.jar\r\n                                                                 [classpath] E:\\neo4j\\neo4j-community-2025.08.0-windows\\neo4j-community-2025.08.0\\lib\\neo4j-resource-2025.08.0.jar\r\n                                                                 [classpath] E:\\neo4j\\neo4j-community-2025.08.0-windows\\neo4j-community-2025.08.0\\lib\\neo4j-cypher-runtime-util-2025.08.0.jar\r\n                                                                 [classpath] E:\\neo4j\\neo4j-community-2025.08.0-windows\\neo4j-community-2025.08.0\\lib\\commons-codec-1.18.0.jar\r\n                                                                 [classpath] E:\\neo4j\\neo4j-community-2025.08.0-windows\\neo4j-community-2025.08.0\\plugins\\*\r\n                                                                 [classpath] E:\\neo4j\\neo4j-community-2025.08.0-windows\\neo4j-community-2025.08.0\\lib\\jetty-security-12.0.17.jar\r\n                                                                 [classpath] E:\\neo4j\\neo4j-community-2025.08.0-windows\\neo4j-community-2025.08.0\\lib\\arrow-memory-netty-18.3.0.jar\r\n                                                                 [classpath] E:\\neo4j\\neo4j-community-2025.08.0-windows\\neo4j-community-2025.08.0\\lib\\netty-transport-4.2.4.Final.jar\r\n                                                                 [classpath] E:\\neo4j\\neo4j-community-2025.08.0-windows\\neo4j-community-2025.08.0\\lib\\jetty-ee8-servlet-12.0.17.jar\r\n                                                                 [classpath] E:\\neo4j\\neo4j-community-2025.08.0-windows\\neo4j-community-2025.08.0\\lib\\snappy-java-1.1.10.7.jar\r\n                                                                 [classpath] E:\\neo4j\\neo4j-community-2025.08.0-windows\\neo4j-community-2025.08.0\\lib\\jettison-1.5.4.jar\r\n                                                                 [classpath] E:\\neo4j\\neo4j-community-2025.08.0-windows\\neo4j-community-2025.08.0\\lib\\commons-lang3-3.18.0.jar\r\n                                                                 [classpath] E:\\neo4j\\neo4j-community-2025.08.0-windows\\neo4j-community-2025.08.0\\lib\\log4j-layout-template-json-2.20.0.jar\r\n                                                                 [classpath] E:\\neo4j\\neo4j-community-2025.08.0-windows\\neo4j-community-2025.08.0\\lib\\flatbuffers-java-25.2.10.jar\r\n                                                                 [classpath] E:\\neo4j\\neo4j-community-2025.08.0-windows\\neo4j-community-2025.08.0\\lib\\jetty-alpn-java-server-12.0.17.jar"}
{"time":"2025-09-22 12:30:17.074+0000","level":"INFO","category":"o.n.i.d.DiagnosticsManager","message":"\r\n                                                                 [classpath] E:\\neo4j\\neo4j-community-2025.08.0-windows\\neo4j-community-2025.08.0\\lib\\neo4j-browser-ce-2025.8.0.jar\r\n                                                                 [classpath] E:\\neo4j\\neo4j-community-2025.08.0-windows\\neo4j-community-2025.08.0\\lib\\jetty-util-12.0.17.jar\r\n                                                                 [classpath] E:\\neo4j\\neo4j-community-2025.08.0-windows\\neo4j-community-2025.08.0\\lib\\cypher-v5-literal-interpreter-2025.08.0.jar\r\n                                                                 [classpath] E:\\neo4j\\neo4j-community-2025.08.0-windows\\neo4j-community-2025.08.0\\lib\\neo4j-gql-status-2025.08.0.jar\r\n                                                                 [classpath] E:\\neo4j\\neo4j-community-2025.08.0-windows\\neo4j-community-2025.08.0\\lib\\neo4j-slf4j-provider-2025.08.0.jar\r\n                                                                 [classpath] E:\\neo4j\\neo4j-community-2025.08.0-windows\\neo4j-community-2025.08.0\\lib\\netty-transport-classes-kqueue-4.2.4.Final.jar\r\n                                                                 [classpath] E:\\neo4j\\neo4j-community-2025.08.0-windows\\neo4j-community-2025.08.0\\lib\\neo4j-graph-algo-2025.08.0.jar\r\n                                                                 [classpath] E:\\neo4j\\neo4j-community-2025.08.0-windows\\neo4j-community-2025.08.0\\lib\\jetty-alpn-server-12.0.17.jar\r\n                                                                 [classpath] E:\\neo4j\\neo4j-community-2025.08.0-windows\\neo4j-community-2025.08.0\\lib\\scala-library-2.13.16.jar\r\n                                                                 [classpath] E:\\neo4j\\neo4j-community-2025.08.0-windows\\neo4j-community-2025.08.0\\lib\\aircompressor-2.0.2.jar\r\n                                                                 [classpath] E:\\neo4j\\neo4j-community-2025.08.0-windows\\neo4j-community-2025.08.0\\lib\\cypher-antlr-ast-common-2025.08.0.jar\r\n                                                                 [classpath] E:\\neo4j\\neo4j-community-2025.08.0-windows\\neo4j-community-2025.08.0\\lib\\protobuf-java-4.31.1.jar\r\n                                                                 [classpath] E:\\neo4j\\neo4j-community-2025.08.0-windows\\neo4j-community-2025.08.0\\lib\\jakarta.ws.rs-api-2.1.6.jar\r\n                                                                 [classpath] E:\\neo4j\\neo4j-community-2025.08.0-windows\\neo4j-community-2025.08.0\\lib\\neo4j-wal-2025.08.0.jar\r\n                                                                 [classpath] E:\\neo4j\\neo4j-community-2025.08.0-windows\\neo4j-community-2025.08.0\\lib\\jersey-common-2.43.jar\r\n                                                                 [classpath] E:\\neo4j\\neo4j-community-2025.08.0-windows\\neo4j-community-2025.08.0\\lib\\neo4j-import-util-2025.08.0.jar\r\n                                                                 [classpath] E:\\neo4j\\neo4j-community-2025.08.0-windows\\neo4j-community-2025.08.0\\lib\\neo4j-kernel-api-2025.08.0.jar\r\n                                                                 [classpath] E:\\neo4j\\neo4j-community-2025.08.0-windows\\neo4j-community-2025.08.0\\lib\\shiro-hashes-bcrypt-2.0.5.jar\r\n                                                                 [classpath] E:\\neo4j\\neo4j-community-2025.08.0-windows\\neo4j-community-2025.08.0\\lib\\parquet-common-1.15.2.jar\r\n                                                                 [classpath] E:\\neo4j\\neo4j-community-2025.08.0-windows\\neo4j-community-2025.08.0\\lib\\cypher-v25-antlr-parser-2025.08.0.jar\r\n                                                                 [classpath] E:\\neo4j\\neo4j-community-2025.08.0-windows\\neo4j-community-2025.08.0\\lib\\lucene-analysis-common-9.11.1.jar\r\n                                                                 [classpath] E:\\neo4j\\neo4j-community-2025.08.0-windows\\neo4j-community-2025.08.0\\lib\\neo4j-graphdb-api-2025.08.0.jar\r\n                                                                 [classpath] E:\\neo4j\\neo4j-community-2025.08.0-windows\\neo4j-community-2025.08.0\\lib\\ipaddress-5.5.1.jar\r\n                                                                 [classpath] E:\\neo4j\\neo4j-community-2025.08.0-windows\\neo4j-community-2025.08.0\\lib\\neo4j-procedure-2025.08.0.jar\r\n                                                                 [classpath] E:\\neo4j\\neo4j-community-2025.08.0-windows\\neo4j-community-2025.08.0\\lib\\netty-resolver-4.2.4.Final.jar\r\n                                                                 [classpath] E:\\neo4j\\neo4j-community-2025.08.0-windows\\neo4j-community-2025.08.0\\lib\\neo4j-query-router-2025.08.0.jar\r\n                                                                 [classpath] E:\\neo4j\\neo4j-community-2025.08.0-windows\\neo4j-community-2025.08.0\\lib\\netty-transport-classes-epoll-4.2.4.Final.jar\r\n                                                                 [classpath] E:\\neo4j\\neo4j-community-2025.08.0-windows\\neo4j-community-2025.08.0\\lib\\shiro-core-2.0.5.jar\r\n                                                                 [classpath] E:\\neo4j\\neo4j-community-2025.08.0-windows\\neo4j-community-2025.08.0\\lib\\jetty-session-12.0.17.jar\r\n                                                                 [classpath] E:\\neo4j\\neo4j-community-2025.08.0-windows\\neo4j-community-2025.08.0\\lib\\jetty-http2-common-12.0.17.jar\r\n                                                                 [classpath] E:\\neo4j\\neo4j-community-2025.08.0-windows\\neo4j-community-2025.08.0\\lib\\jetty-io-12.0.17.jar\r\n                                                                 [classpath] E:\\neo4j\\neo4j-community-2025.08.0-windows\\neo4j-community-2025.08.0\\lib\\neo4j-lock-2025.08.0.jar\r\n                                                                 [classpath] E:\\neo4j\\neo4j-community-2025.08.0-windows\\neo4j-community-2025.08.0\\lib\\neo4j-cypher-interpreted-runtime-2025.08.0.jar\r\n                                                                 [classpath] E:\\neo4j\\neo4j-community-2025.08.0-windows\\neo4j-community-2025.08.0\\lib\\asm-analysis-9.8.jar\r\n                                                                 [classpath] E:\\neo4j\\neo4j-community-2025.08.0-windows\\neo4j-community-2025.08.0\\lib\\jackson-core-2.19.1.jar\r\n                                                                 [classpath] E:\\neo4j\\neo4j-community-2025.08.0-windows\\neo4j-community-2025.08.0\\lib\\neo4j-server-2025.08.0.jar\r\n                                                                 [classpath] E:\\neo4j\\neo4j-community-2025.08.0-windows\\neo4j-community-2025.08.0\\lib\\neo4j-index-2025.08.0.jar\r\n                                                                 [classpath] E:\\neo4j\\neo4j-community-2025.08.0-windows\\neo4j-community-2025.08.0\\lib\\server-api-2025.08.0.jar\r\n                                                                 [classpath] E:\\neo4j\\neo4j-community-2025.08.0-windows\\neo4j-community-2025.08.0\\lib\\neo4j-bolt-connection-6.0.2.jar\r\n                                                                 [classpath] E:\\neo4j\\neo4j-community-2025.08.0-windows\\neo4j-community-2025.08.0\\lib\\perfmark-api-0.27.0.jar\r\n                                                                 [classpath] E:\\neo4j\\neo4j-community-2025.08.0-windows\\neo4j-community-2025.08.0\\lib\\netty-handler-proxy-4.2.4.Final.jar\r\n                                                                 [classpath] E:\\neo4j\\neo4j-community-2025.08.0-windows\\neo4j-community-2025.08.0\\lib\\jersey-container-servlet-core-2.43.jar\r\n                                                                 [classpath] E:\\neo4j\\neo4j-community-2025.08.0-windows\\neo4j-community-2025.08.0\\lib\\shiro-hashes-argon2-2.0.5.jar\r\n                                                                 [classpath] E:\\neo4j\\neo4j-community-2025.08.0-windows\\neo4j-community-2025.08.0\\lib\\netty-codec-compression-4.2.4.Final.jar\r\n                                                                 [classpath] E:\\neo4j\\neo4j-community-2025.08.0-windows\\neo4j-community-2025.08.0\\lib\\failureaccess-1.0.3.jar\r\n                                                                 [classpath] E:\\neo4j\\neo4j-community-2025.08.0-windows\\neo4j-community-2025.08.0\\lib\\eclipse-collections-api-11.1.0.jar\r\n                                                                 [classpath] E:\\neo4j\\neo4j-community-2025.08.0-windows\\neo4j-community-2025.08.0\\lib\\neo4j-csv-2025.08.0.jar\r\n                                                                 [classpath] E:\\neo4j\\neo4j-community-2025.08.0-windows\\neo4j-community-2025.08.0\\lib\\picocli-4.7.7.jar\r\n                                                                 [classpath] E:\\neo4j\\neo4j-community-2025.08.0-windows\\neo4j-community-2025.08.0\\lib\\netty-codec-socks-4.2.4.Final.jar\r\n                                                                 [classpath] E:\\neo4j\\neo4j-community-2025.08.0-windows\\neo4j-community-2025.08.0\\lib\\neo4j-import-api-2025.08.0.jar"}
{"time":"2025-09-22 12:30:17.075+0000","level":"INFO","category":"o.n.i.d.DiagnosticsManager","message":"\r\n                                                                 [classpath] E:\\neo4j\\neo4j-community-2025.08.0-windows\\neo4j-community-2025.08.0\\lib\\protobuf-java-util-4.31.1.jar\r\n                                                                 [classpath] E:\\neo4j\\neo4j-community-2025.08.0-windows\\neo4j-community-2025.08.0\\lib\\parquet-format-structures-1.15.2.jar\r\n                                                                 [classpath] E:\\neo4j\\neo4j-community-2025.08.0-windows\\neo4j-community-2025.08.0\\lib\\jackson-module-jaxb-annotations-2.19.1.jar\r\n                                                                 [classpath] E:\\neo4j\\neo4j-community-2025.08.0-windows\\neo4j-community-2025.08.0\\lib\\neo4j-cypher-rendering-2025.08.0.jar\r\n                                                                 [classpath] E:\\neo4j\\neo4j-community-2025.08.0-windows\\neo4j-community-2025.08.0\\lib\\kiama_2.13-2.5.1.jar\r\n                                                                 [classpath] E:\\neo4j\\neo4j-community-2025.08.0-windows\\neo4j-community-2025.08.0\\lib\\shiro-crypto-hash-2.0.5.jar\r\n                                                                 [classpath] E:\\neo4j\\neo4j-community-2025.08.0-windows\\neo4j-community-2025.08.0\\lib\\grpc-context-1.73.0.jar\r\n                                                                 [classpath] E:\\neo4j\\neo4j-community-2025.08.0-windows\\neo4j-community-2025.08.0\\lib\\neo4j-concurrent-2025.08.0.jar\r\n                                                                 [classpath] E:\\neo4j\\neo4j-community-2025.08.0-windows\\neo4j-community-2025.08.0\\lib\\istack-commons-runtime-3.0.8.jar\r\n                                                                 [classpath] E:\\neo4j\\neo4j-community-2025.08.0-windows\\neo4j-community-2025.08.0\\lib\\jetty-ee-12.0.17.jar\r\n                                                                 [classpath] E:\\neo4j\\neo4j-community-2025.08.0-windows\\neo4j-community-2025.08.0\\lib\\netty-tcnative-classes-2.0.72.Final.jar\r\n                                                                 [classpath] E:\\neo4j\\neo4j-community-2025.08.0-windows\\neo4j-community-2025.08.0\\lib\\cypher-parser-factory-2025.08.0.jar\r\n                                                                 [classpath] E:\\neo4j\\neo4j-community-2025.08.0-windows\\neo4j-community-2025.08.0\\lib\\neo4j-bolt-connection-pooled-6.0.2.jar\r\n                                                                 [classpath] E:\\neo4j\\neo4j-community-2025.08.0-windows\\neo4j-community-2025.08.0\\lib\\commons-io-2.19.0.jar\r\n                                                                 [classpath] E:\\neo4j\\neo4j-community-2025.08.0-windows\\neo4j-community-2025.08.0\\lib\\shiro-crypto-core-2.0.5.jar\r\n                                                                 [classpath] E:\\neo4j\\neo4j-community-2025.08.0-windows\\neo4j-community-2025.08.0\\lib\\lucene-core-9.11.1.jar\r\n                                                                 [classpath] E:\\neo4j\\neo4j-community-2025.08.0-windows\\neo4j-community-2025.08.0\\lib\\neo4j-common-2025.08.0.jar\r\n                                                                 [classpath] E:\\neo4j\\neo4j-community-2025.08.0-windows\\neo4j-community-2025.08.0\\lib\\neo4j-cloud-2025.08.0.jar\r\n                                                                 [classpath] E:\\neo4j\\neo4j-community-2025.08.0-windows\\neo4j-community-2025.08.0\\lib\\zstd-proxy-2025.08.0.jar\r\n                                                                 [classpath] E:\\neo4j\\neo4j-community-2025.08.0-windows\\neo4j-community-2025.08.0\\lib\\hk2-utils-2.6.1.jar\r\n                                                                 [classpath] E:\\neo4j\\neo4j-community-2025.08.0-windows\\neo4j-community-2025.08.0\\lib\\neo4j-id-generator-2025.08.0.jar\r\n                                                                 [classpath] E:\\neo4j\\neo4j-community-2025.08.0-windows\\neo4j-community-2025.08.0\\lib\\netty-common-4.2.4.Final.jar\r\n                                                                 [classpath] E:\\neo4j\\neo4j-community-2025.08.0-windows\\neo4j-community-2025.08.0\\lib\\jline-terminal-3.21.0.jar\r\n                                                                 [classpath] E:\\neo4j\\neo4j-community-2025.08.0-windows\\neo4j-community-2025.08.0\\lib\\neo4j-cypher-ir-2025.08.0.jar\r\n                                                                 [classpath] E:\\neo4j\\neo4j-community-2025.08.0-windows\\neo4j-community-2025.08.0\\lib\\neo4j-configuration-2025.08.0.jar\r\n                                                                 [classpath] E:\\neo4j\\neo4j-community-2025.08.0-windows\\neo4j-community-2025.08.0\\lib\\asm-9.8.jar\r\n                                                                 [classpath] E:\\neo4j\\neo4j-community-2025.08.0-windows\\neo4j-community-2025.08.0\\lib\\lucene-queryparser-9.11.1.jar\r\n                                                                 [classpath] E:\\neo4j\\neo4j-community-2025.08.0-windows\\neo4j-community-2025.08.0\\lib\\neo4j-io-2025.08.0.jar\r\n                                                                 [classpath] E:\\neo4j\\neo4j-community-2025.08.0-windows\\neo4j-community-2025.08.0\\lib\\gson-2.11.0.jar\r\n                                                                 [classpath] E:\\neo4j\\neo4j-community-2025.08.0-windows\\neo4j-community-2025.08.0\\lib\\cypher-v5-antlr-parser-2025.08.0.jar\r\n                                                                 [classpath] E:\\neo4j\\neo4j-community-2025.08.0-windows\\neo4j-community-2025.08.0\\lib\\neo4j-push-to-cloud-2025.08.0.jar\r\n                                                                 [classpath] E:\\neo4j\\neo4j-community-2025.08.0-windows\\neo4j-community-2025.08.0\\lib\\commons-text-1.13.1.jar\r\n                                                                 [classpath] E:\\neo4j\\neo4j-community-2025.08.0-windows\\neo4j-community-2025.08.0\\lib\\argparse4j-0.9.0.jar\r\n                                                                 [classpath] E:\\neo4j\\neo4j-community-2025.08.0-windows\\neo4j-community-2025.08.0\\lib\\netty-handler-4.2.4.Final.jar\r\n                                                                 [classpath] E:\\neo4j\\neo4j-community-2025.08.0-windows\\neo4j-community-2025.08.0\\lib\\neo4j-storage-engine-util-2025.08.0.jar\r\n                                                                 [classpath] E:\\neo4j\\neo4j-community-2025.08.0-windows\\neo4j-community-2025.08.0\\lib\\jline-reader-3.21.0.jar\r\n                                                                 [classpath] E:\\neo4j\\neo4j-community-2025.08.0-windows\\neo4j-community-2025.08.0\\lib\\neo4j-cypher-macros-2025.08.0.jar\r\n                                                                 [classpath] E:\\neo4j\\neo4j-community-2025.08.0-windows\\neo4j-community-2025.08.0\\lib\\jProcesses-1.6.5.jar\r\n                                                                 [classpath] E:\\neo4j\\neo4j-community-2025.08.0-windows\\neo4j-community-2025.08.0\\lib\\log4j-core-2.20.0.jar\r\n                                                                 [classpath] E:\\neo4j\\neo4j-community-2025.08.0-windows\\neo4j-community-2025.08.0\\lib\\neo4j-import-tool-2025.08.0.jar\r\n                                                                 [classpath] E:\\neo4j\\neo4j-community-2025.08.0-windows\\neo4j-community-2025.08.0\\lib\\neo4j-cypher-expression-evaluator-2025.08.0.jar\r\n                                                                 [classpath] E:\\neo4j\\neo4j-community-2025.08.0-windows\\neo4j-community-2025.08.0\\lib\\jetty-http-12.0.17.jar\r\n                                                                 [classpath] E:\\neo4j\\neo4j-community-2025.08.0-windows\\neo4j-community-2025.08.0\\lib\\jakarta.inject-2.6.1.jar\r\n                                                                 [classpath] E:\\neo4j\\neo4j-community-2025.08.0-windows\\neo4j-community-2025.08.0\\lib\\jetty-servlet-api-4.0.6.jar\r\n                                                                 [classpath] E:\\neo4j\\neo4j-community-2025.08.0-windows\\neo4j-community-2025.08.0\\lib\\neo4j-schema-2025.08.0.jar\r\n                                                                 [classpath] E:\\neo4j\\neo4j-community-2025.08.0-windows\\neo4j-community-2025.08.0\\lib\\jetty-ee8-security-12.0.17.jar\r\n                                                                 [classpath] E:\\neo4j\\neo4j-community-2025.08.0-windows\\neo4j-community-2025.08.0\\lib\\grpc-protobuf-lite-1.73.0.jar\r\n                                                                 [classpath] E:\\neo4j\\neo4j-community-2025.08.0-windows\\neo4j-community-2025.08.0\\lib\\grpc-api-1.73.0.jar\r\n                                                                 [classpath] E:\\neo4j\\neo4j-community-2025.08.0-windows\\neo4j-community-2025.08.0\\lib\\shiro-cache-2.0.5.jar\r\n                                                                 [classpath] E:\\neo4j\\neo4j-community-2025.08.0-windows\\neo4j-community-2025.08.0\\lib\\neo4j-monitoring-2025.08.0.jar"}
{"time":"2025-09-22 12:30:17.076+0000","level":"INFO","category":"o.n.i.d.DiagnosticsManager","message":"\r\n                                                                 [classpath] E:\\neo4j\\neo4j-community-2025.08.0-windows\\neo4j-community-2025.08.0\\lib\\netty-codec-http-4.2.4.Final.jar\r\n                                                                 [classpath] E:\\neo4j\\neo4j-community-2025.08.0-windows\\neo4j-community-2025.08.0\\lib\\grpc-stub-1.73.0.jar\r\n                                                                 [classpath] E:\\neo4j\\neo4j-community-2025.08.0-windows\\neo4j-community-2025.08.0\\lib\\hk2-api-2.6.1.jar\r\n                                                                 [classpath] E:\\neo4j\\neo4j-community-2025.08.0-windows\\neo4j-community-2025.08.0\\lib\\neo4j-security-2025.08.0.jar\r\n                                                                 [classpath] E:\\neo4j\\neo4j-community-2025.08.0-windows\\neo4j-community-2025.08.0\\lib\\neo4j-logging-2025.08.0.jar\r\n                                                                 [classpath] E:\\neo4j\\neo4j-community-2025.08.0-windows\\neo4j-community-2025.08.0\\lib\\arrow-memory-core-18.3.0.jar\r\n                                                                 [classpath] E:\\neo4j\\neo4j-community-2025.08.0-windows\\neo4j-community-2025.08.0\\lib\\neo4j-ssl-2025.08.0.jar\r\n                                                                 [classpath] E:\\neo4j\\neo4j-community-2025.08.0-windows\\neo4j-community-2025.08.0\\lib\\FastInfoset-1.2.16.jar\r\n                                                                "}
{"time":"2025-09-22 12:30:17.077+0000","level":"INFO","category":"o.n.i.d.DiagnosticsManager","message":"\r\n                                                                --------------------------------------------------------------------------------\r\n                                                                                                [ Library path ]                                \r\n                                                                --------------------------------------------------------------------------------\r\n                                                                E:\\java\\bin\r\n                                                                C:\\Windows\\Sun\\Java\\bin\r\n                                                                C:\\Windows\\System32\r\n                                                                C:\\Windows\r\n                                                                E:\\conda\r\n                                                                E:\\conda\\Library\\mingw-w64\\bin\r\n                                                                E:\\conda\\Library\\usr\\bin\r\n                                                                E:\\conda\\Library\\bin\r\n                                                                E:\\conda\\Scripts\r\n                                                                E:\\conda\\bin\r\n                                                                C:\\Windows\\System32\r\n                                                                C:\\Windows\r\n                                                                C:\\Windows\\System32\\wbem\r\n                                                                C:\\Windows\\System32\\WindowsPowerShell\\v1.0\r\n                                                                C:\\Windows\\System32\\OpenSSH\r\n                                                                C:\\Program Files\\Docker\\Docker\\resources\\bin\r\n                                                                D:\\trae\r\n                                                                D:\\node.js\r\n                                                                D:\\git\r\n                                                                D:\\git\\git\\Git\\cmd\r\n                                                                C:\\Program Files\\dotnet\r\n                                                                C:\\Users\\<USER>\\AppData\\Roaming\\npm\r\n                                                                C:\\Users\\<USER>\\AppData\\Local\\Muse Hub\\lib\r\n                                                                C:\\ProgramData\\chocolatey\\bin\r\n                                                                C:\\Program Files\\gs\\gs10.05.1\\bin\r\n                                                                C:\\ProgramData\\chocolatey\\lib\\poppler\\tools\\poppler-25.07.0\\bin\r\n                                                                C:\\Users\\<USER>\\.trae\\sdks\\workspaces\\c47c7beb\\versions\\node\\current\r\n                                                                C:\\Users\\<USER>\\.trae\\sdks\\versions\\node\\current\r\n                                                                C:\\Users\\<USER>\\.trae\\sdks\\workspaces\\c47c7beb\\versions\\node\\current\r\n                                                                C:\\Users\\<USER>\\.trae\\sdks\\versions\\node\\current\r\n                                                                C:\\Windows\\System32\r\n                                                                C:\\Windows\r\n                                                                C:\\Windows\\System32\\wbem\r\n                                                                C:\\Windows\\System32\\WindowsPowerShell\\v1.0\r\n                                                                C:\\Windows\\System32\\OpenSSH\r\n                                                                C:\\Program Files\\Docker\\Docker\\resources\\bin\r\n                                                                D:\\trae\r\n                                                                D:\\node.js\r\n                                                                D:\\git\r\n                                                                D:\\git\\git\\Git\\cmd\r\n                                                                C:\\Program Files\\dotnet\r\n                                                                C:\\Users\\<USER>\\AppData\\Roaming\\npm\r\n                                                                C:\\Users\\<USER>\\AppData\\Local\\Muse Hub\\lib\r\n                                                                C:\\ProgramData\\chocolatey\\bin\r\n                                                                C:\\Program Files\\gs\\gs10.05.1\\bin\r\n                                                                C:\\ProgramData\\chocolatey\\lib\\poppler\\tools\\poppler-25.07.0\\bin\r\n                                                                E:\\python\\Scripts\r\n                                                                E:\\python\r\n                                                                C:\\Users\\<USER>\\AppData\\Local\\Microsoft\\WindowsApps\r\n                                                                D:\\git\r\n                                                                C:\\Users\\<USER>\\AppData\\Local\\Muse Hub\\lib\r\n                                                                E:\\VSCode\\Microsoft VS Code\\bin\r\n                                                                C:\\Users\\<USER>\\AppData\\Roaming\\npm\r\n                                                                C:\\Users\\<USER>\\AppData\\Local\\Pandoc\r\n                                                                C:\\Program Files\\Tesseract-OCR\r\n                                                                C:\\ProgramData\\chocolatey\\lib\\poppler\\tools\\poppler-25.07.0\\bin\r\n                                                                E:\\kiro\\Kiro\\bin\r\n                                                                C:\\Users\\<USER>\\.trae\\extensions\\ms-python.debugpy-2025.6.0-win32-x64\\bundled\\scripts\\noConfigScripts\r\n                                                                E:\\conda\r\n                                                                E:\\conda\\Scripts\r\n                                                                E:\\conda\\condabin\r\n                                                                E:\\cursor\\cursor\\resources\\app\\bin\r\n                                                                C:\\Users\\<USER>\\AppData\\Local\\Programs\\Ollama\r\n                                                                E:\\neo4j\\neo4j-community-2025.08.0-windows\\neo4j-community-2025.08.0\r\n                                                                "}
{"time":"2025-09-22 12:30:17.078+0000","level":"INFO","category":"o.n.i.d.DiagnosticsManager","message":"\r\n                                                                --------------------------------------------------------------------------------\r\n                                                                                             [ System properties ]                              \r\n                                                                --------------------------------------------------------------------------------\r\n                                                                jdk.tls.rejectClientInitiatedRenegotiation = true\r\n                                                                sun.cpu.isalist = amd64\r\n                                                                sun.jnu.encoding = GBK\r\n                                                                log4j.layout.jsonTemplate.maxStringLength = 32768\r\n                                                                sun.arch.data.model = 64\r\n                                                                user.variant = \r\n                                                                user.timezone = Asia/Shanghai\r\n                                                                io.netty.leakDetection.level = DISABLED\r\n                                                                sun.java.launcher = SUN_STANDARD\r\n                                                                user.country = CN\r\n                                                                sun.boot.library.path = E:\\java\\bin\r\n                                                                sun.java.command = org.neo4j.server.Neo4jCommunity --home-dir=E:\\neo4j\\neo4j-community-2025.08.0-windows\\neo4j-community-2025.08.0 --config-dir=E:\\neo4j\\neo4j-community-2025.08.0-windows\\neo4j-community-2025.08.0\\conf --console-mode\r\n                                                                jdk.debug = release\r\n                                                                io.netty.tryReflectionSetAccessible = true\r\n                                                                sun.cpu.endian = little\r\n                                                                user.home = C:\\Users\\<USER>\r\n                                                                user.language = zh\r\n                                                                file.separator = \\\r\n                                                                jdk.tls.ephemeralDHKeySize = 2048\r\n                                                                user.script = \r\n                                                                sun.management.compiler = HotSpot 64-Bit Tiered Compilers\r\n                                                                user.name = Administrator\r\n                                                                stdout.encoding = ms936\r\n                                                                jdk.nio.maxCachedBufferSize = 1024\r\n                                                                path.separator = ;\r\n                                                                file.encoding = UTF-8\r\n                                                                jnidispatch.path = C:\\Users\\<USER>\\AppData\\Local\\Temp\\jna-146731693\\jna1986547551927049460.dll\r\n                                                                jna.loaded = true\r\n                                                                user.dir = E:\\neo4j\\neo4j-community-2025.08.0-windows\\neo4j-community-2025.08.0\r\n                                                                sun.os.patch.level = \r\n                                                                native.encoding = GBK\r\n                                                                stderr.encoding = ms936\r\n                                                                sun.io.unicode.encoding = UnicodeLittle\r\n                                                                log4j2.disable.jmx = true\r\n                                                                "}
{"time":"2025-09-22 12:30:17.078+0000","level":"INFO","category":"o.n.i.d.DiagnosticsManager","message":"\r\n                                                                --------------------------------------------------------------------------------\r\n                                                                                      [ (IANA) TimeZone database version ]                      \r\n                                                                --------------------------------------------------------------------------------\r\n                                                                  TimeZone version: 2025b (available for 604 zone identifiers)\r\n                                                                "}
{"time":"2025-09-22 12:30:17.078+0000","level":"INFO","category":"o.n.i.d.DiagnosticsManager","message":"\r\n                                                                --------------------------------------------------------------------------------\r\n                                                                                            [ Network information ]                             \r\n                                                                --------------------------------------------------------------------------------\r\n                                                                Interface WAN Miniport (IP)-WFP Native MAC Layer LightWeight Filter-0000:\r\n                                                                Interface WAN Miniport (IP)-QoS Packet Scheduler-0000:\r\n                                                                Interface WAN Miniport (IPv6)-WFP Native MAC Layer LightWeight Filter-0000:\r\n                                                                Interface Hyper-V Virtual Switch Extension Adapter-Hyper-V Virtual Switch Extension Filter-0000:\r\n                                                                Interface WAN Miniport (IPv6)-QoS Packet Scheduler-0000:\r\n                                                                Interface Hyper-V Virtual Ethernet Adapter-WFP Native MAC Layer LightWeight Filter-0000:\r\n                                                                Interface Hyper-V Virtual Ethernet Adapter-QoS Packet Scheduler-0000:\r\n                                                                Interface WAN Miniport (Network Monitor)-WFP Native MAC Layer LightWeight Filter-0000:\r\n                                                                Interface Realtek PCIe GbE Family Controller-WFP Native MAC Layer LightWeight Filter-0000:\r\n                                                                Interface Realtek PCIe GbE Family Controller-QoS Packet Scheduler-0000:\r\n                                                                Interface Realtek PCIe GbE Family Controller-WFP 802.3 MAC Layer LightWeight Filter-0000:\r\n                                                                Interface WAN Miniport (Network Monitor)-QoS Packet Scheduler-0000:\r\n                                                                Interface Hyper-V Virtual Ethernet Adapter-WFP 802.3 MAC Layer LightWeight Filter-0000:\r\n                                                                Interface Hyper-V Virtual Switch Extension Adapter #2-Hyper-V Virtual Switch Extension Filter-0000:\r\n                                                                Interface Hyper-V Virtual Ethernet Adapter #2-WFP Native MAC Layer LightWeight Filter-0000:\r\n                                                                Interface Hyper-V Virtual Ethernet Adapter #2-QoS Packet Scheduler-0000:\r\n                                                                Interface Hyper-V Virtual Ethernet Adapter #2-WFP 802.3 MAC Layer LightWeight Filter-0000:\r\n                                                                Interface Microsoft Kernel Debug Network Adapter:\r\n                                                                Interface Realtek PCIe GbE Family Controller:\r\n                                                                    address: fe80:0:0:0:c25a:79:75e8:6d90%ethernet_32769\r\n                                                                Interface Bluetooth Device (Personal Area Network):\r\n                                                                Interface WAN Miniport (IP):\r\n                                                                Interface WAN Miniport (IPv6):\r\n                                                                Interface WAN Miniport (Network Monitor):\r\n                                                                Interface Hyper-V Virtual Switch Extension Adapter:\r\n                                                                Interface Hyper-V Virtual Ethernet Adapter:\r\n                                                                    address: fe80:0:0:0:1a68:e38c:f878:e658%ethernet_32775\r\n                                                                    address: *************\r\n                                                                Interface Hyper-V Virtual Switch Extension Adapter #2:\r\n                                                                Interface Hyper-V Virtual Ethernet Adapter #2:\r\n                                                                    address: fe80:0:0:0:4929:732b:f43f:aa4c%ethernet_32777\r\n                                                                    address: ************\r\n                                                                Interface WAN Miniport (PPPOE):\r\n                                                                Interface Software Loopback Interface 1:\r\n                                                                    address: 0:0:0:0:0:0:0:1\r\n                                                                    address: 127.0.0.1\r\n                                                                Interface Meta Tunnel:\r\n                                                                    address: fdfe:dcba:9876:0:0:0:0:1\r\n                                                                    address: **********\r\n                                                                Interface Intel(R) Wi-Fi 6 AX200 160MHz-WFP Native MAC Layer LightWeight Filter-0000:\r\n                                                                Interface Intel(R) Wi-Fi 6 AX200 160MHz-Virtual WiFi Filter Driver-0000:\r\n                                                                Interface Intel(R) Wi-Fi 6 AX200 160MHz-Native WiFi Filter Driver-0000:\r\n                                                                Interface Intel(R) Wi-Fi 6 AX200 160MHz-QoS Packet Scheduler-0000:\r\n                                                                Interface Intel(R) Wi-Fi 6 AX200 160MHz-WFP 802.3 MAC Layer LightWeight Filter-0000:\r\n                                                                Interface Microsoft Wi-Fi Direct Virtual Adapter-WFP Native MAC Layer LightWeight Filter-0000:\r\n                                                                Interface Microsoft Wi-Fi Direct Virtual Adapter-Native WiFi Filter Driver-0000:\r\n                                                                Interface Microsoft Wi-Fi Direct Virtual Adapter-QoS Packet Scheduler-0000:\r\n                                                                Interface Microsoft Wi-Fi Direct Virtual Adapter-WFP 802.3 MAC Layer LightWeight Filter-0000:\r\n                                                                Interface Microsoft Wi-Fi Direct Virtual Adapter #2-WFP Native MAC Layer LightWeight Filter-0000:\r\n                                                                Interface Microsoft Wi-Fi Direct Virtual Adapter #2-Native WiFi Filter Driver-0000:\r\n                                                                Interface Microsoft Wi-Fi Direct Virtual Adapter #2-QoS Packet Scheduler-0000:\r\n                                                                Interface Microsoft Wi-Fi Direct Virtual Adapter #2-WFP 802.3 MAC Layer LightWeight Filter-0000:\r\n                                                                Interface Intel(R) Wi-Fi 6 AX200 160MHz:\r\n                                                                    address: fe80:0:0:0:592e:4e99:26e4:b1f5%wireless_32768\r\n                                                                    address: 2408:820c:820a:1760:9ea1:537b:5307:24e\r\n                                                                    address: 2408:820c:820a:1760:206d:eb1e:620:c820\r\n                                                                    address: 2408:820c:820a:1760:0:0:0:6fa\r\n                                                                    address: ************\r\n                                                                Interface Microsoft Wi-Fi Direct Virtual Adapter:\r\n                                                                    address: fe80:0:0:0:dcb:7296:b3b8:d60b%wireless_32769\r\n                                                                Interface Microsoft Wi-Fi Direct Virtual Adapter #2:\r\n                                                                    address: fe80:0:0:0:11f0:af32:4c68:7d36%wireless_32770\r\n                                                                Interface Microsoft Teredo Tunneling Adapter:\r\n                                                                Interface Microsoft IP-HTTPS Platform Adapter:\r\n                                                                Interface Microsoft 6to4 Adapter:\r\n                                                                Interface WAN Miniport (SSTP):\r\n                                                                Interface WAN Miniport (IKEv2):\r\n                                                                Interface WAN Miniport (L2TP):\r\n                                                                Interface WAN Miniport (PPTP):\r\n                                                                "}
{"time":"2025-09-22 12:30:17.079+0000","level":"INFO","category":"o.n.i.d.DiagnosticsManager","message":"\r\n                                                                --------------------------------------------------------------------------------\r\n                                                                                         [ Native access information ]                          \r\n                                                                --------------------------------------------------------------------------------\r\n                                                                Native access details: Native access is not available for current platform.\r\n                                                                "}
{"time":"2025-09-22 12:30:17.079+0000","level":"INFO","category":"o.n.i.d.DiagnosticsManager","message":"\r\n                                                                --------------------------------------------------------------------------------\r\n                                                                                                [ DBMS config ]                                 \r\n                                                                --------------------------------------------------------------------------------\r\n                                                                DBMS provided settings:\r\n                                                                internal.dbms.bolt.local_address=ef1d72ab-238a-4cd2-86e2-ab7a47442463\r\n                                                                server.bolt.enabled=true\r\n                                                                server.directories.configuration=E:\\neo4j\\neo4j-community-2025.08.0-windows\\neo4j-community-2025.08.0\\conf\r\n                                                                server.directories.import=E:\\neo4j\\neo4j-community-2025.08.0-windows\\neo4j-community-2025.08.0\\import\r\n                                                                server.directories.neo4j_home=E:\\neo4j\\neo4j-community-2025.08.0-windows\\neo4j-community-2025.08.0\r\n                                                                server.http.enabled=true\r\n                                                                server.https.enabled=false\r\n                                                                server.jvm.additional=-XX:+UseG1GC\r\n-XX:-OmitStackTraceInFastThrow\r\n-XX:+AlwaysPreTouch\r\n-XX:+UnlockExperimentalVMOptions\r\n-XX:+TrustFinalNonStaticFields\r\n-XX:+DisableExplicitGC\r\n-Djdk.nio.maxCachedBufferSize=1024\r\n-Dio.netty.tryReflectionSetAccessible=true\r\n-Dio.netty.leakDetection.level=DISABLED\r\n-Djdk.tls.ephemeralDHKeySize=2048\r\n-Djdk.tls.rejectClientInitiatedRenegotiation=true\r\n-XX:FlightRecorderOptions=stackdepth=256\r\n-XX:+UnlockDiagnosticVMOptions\r\n-XX:+DebugNonSafepoints\r\n--add-opens=java.base/java.nio=ALL-UNNAMED\r\n--add-opens=java.base/java.io=ALL-UNNAMED\r\n--add-opens=java.base/sun.nio.ch=ALL-UNNAMED\r\n--add-opens=java.base/java.util.concurrent=ALL-UNNAMED\r\n--enable-native-access=ALL-UNNAMED\r\n-Dlog4j2.disable.jmx=true\r\n-Dlog4j.layout.jsonTemplate.maxStringLength=32768\r\n                                                                server.windows_service_name=neo4j\r\n                                                                Directories in use:\r\n                                                                server.directories.configuration=E:\\neo4j\\neo4j-community-2025.08.0-windows\\neo4j-community-2025.08.0\\conf\r\n                                                                server.directories.data=E:\\neo4j\\neo4j-community-2025.08.0-windows\\neo4j-community-2025.08.0\\data\r\n                                                                server.directories.dumps.root=E:\\neo4j\\neo4j-community-2025.08.0-windows\\neo4j-community-2025.08.0\\data\\dumps\r\n                                                                server.directories.import=E:\\neo4j\\neo4j-community-2025.08.0-windows\\neo4j-community-2025.08.0\\import\r\n                                                                server.directories.lib=E:\\neo4j\\neo4j-community-2025.08.0-windows\\neo4j-community-2025.08.0\\lib\r\n                                                                server.directories.licenses=E:\\neo4j\\neo4j-community-2025.08.0-windows\\neo4j-community-2025.08.0\\licenses\r\n                                                                server.directories.logs=E:\\neo4j\\neo4j-community-2025.08.0-windows\\neo4j-community-2025.08.0\\logs\r\n                                                                server.directories.plugins=E:\\neo4j\\neo4j-community-2025.08.0-windows\\neo4j-community-2025.08.0\\plugins\r\n                                                                server.directories.run=E:\\neo4j\\neo4j-community-2025.08.0-windows\\neo4j-community-2025.08.0\\run\r\n                                                                server.directories.script.root=E:\\neo4j\\neo4j-community-2025.08.0-windows\\neo4j-community-2025.08.0\\data\\scripts\r\n                                                                server.directories.transaction.logs.root=E:\\neo4j\\neo4j-community-2025.08.0-windows\\neo4j-community-2025.08.0\\data\\transactions\r\n                                                                "}
{"time":"2025-09-22 12:30:17.080+0000","level":"INFO","category":"o.n.i.d.DiagnosticsManager","message":"\r\n                                                                --------------------------------------------------------------------------------\r\n                                                                                                 [ Packaging ]                                  \r\n                                                                --------------------------------------------------------------------------------\r\n                                                                Edition: Community\r\n                                                                Package Type: tar\r\n                                                                "}
{"time":"2025-09-22 12:30:17.080+0000","level":"INFO","category":"o.n.i.d.DiagnosticsManager","message":"\r\n                                                                --------------------------------------------------------------------------------\r\n                                                                                           [ Global Server Identity ]                           \r\n                                                                --------------------------------------------------------------------------------\r\n                                                                Registered ServerId{b35704b1}\r\n                                                                "}
{"time":"2025-09-22 12:30:17.456+0000","level":"INFO","category":"o.n.b.BoltServer","message":"Using connector transport NIO"}
{"time":"2025-09-22 12:30:17.675+0000","level":"INFO","category":"o.n.b.BoltServer","message":"Configured external Bolt connector with listener address localhost/127.0.0.1:7687"}
{"time":"2025-09-22 12:30:17.679+0000","level":"INFO","category":"o.n.b.BoltServer","message":"Bolt server loaded"}
{"time":"2025-09-22 12:30:17.701+0000","level":"ERROR","category":"o.n.g.f.DatabaseManagementServiceFactory","message":"Error starting Neo4j database server at E:\\neo4j\\neo4j-community-2025.08.0-windows\\neo4j-community-2025.08.0\\data\\databases","stacktrace":"org.neo4j.kernel.lifecycle.LifecycleException: Component 'org.neo4j.kernel.internal.locker.LockerLifecycleAdapter@64a896b0' was successfully initialized, but failed to start. Please see the attached cause exception \"Lock file has been locked by another process: E:\\neo4j\\neo4j-community-2025.08.0-windows\\neo4j-community-2025.08.0\\data\\databases\\store_lock. Please ensure no other process is using this database, and that the directory is writable (required even for read-only access)\".\r\n\tat org.neo4j.kernel.lifecycle.LifeSupport$LifecycleInstance.start(LifeSupport.java:364)\r\n\tat org.neo4j.kernel.lifecycle.LifeSupport.start(LifeSupport.java:91)\r\n\tat org.neo4j.graphdb.facade.DatabaseManagementServiceFactory.startDatabaseServer(DatabaseManagementServiceFactory.java:284)\r\n\tat org.neo4j.graphdb.facade.DatabaseManagementServiceFactory.build(DatabaseManagementServiceFactory.java:227)\r\n\tat org.neo4j.server.CommunityBootstrapper.createNeo(CommunityBootstrapper.java:39)\r\n\tat org.neo4j.server.NeoBootstrapper.start(NeoBootstrapper.java:192)\r\n\tat org.neo4j.server.NeoBootstrapper.start(NeoBootstrapper.java:102)\r\n\tat org.neo4j.server.Neo4jCommunity.main(Neo4jCommunity.java:30)\r\nCaused by: org.neo4j.io.locker.FileLockException: Lock file has been locked by another process: E:\\neo4j\\neo4j-community-2025.08.0-windows\\neo4j-community-2025.08.0\\data\\databases\\store_lock. Please ensure no other process is using this database, and that the directory is writable (required even for read-only access)\r\n\tat org.neo4j.io.locker.Locker.storeLockException(Locker.java:150)\r\n\tat org.neo4j.io.locker.Locker.checkLock(Locker.java:82)\r\n\tat org.neo4j.kernel.internal.locker.GlobalFileLocker.checkLock(GlobalFileLocker.java:55)\r\n\tat org.neo4j.kernel.internal.locker.GlobalLocker.checkLock(GlobalLocker.java:28)\r\n\tat org.neo4j.kernel.internal.locker.LockerLifecycleAdapter.start(LockerLifecycleAdapter.java:34)\r\n\tat org.neo4j.kernel.lifecycle.LifeSupport$LifecycleInstance.start(LifeSupport.java:347)\r\n\t... 7 more\r\n"}
{"time":"2025-09-22 12:30:17.702+0000","level":"INFO","category":"o.n.g.f.DatabaseManagementServiceFactory","message":"Shutdown started"}
{"time":"2025-09-22 12:30:17.703+0000","level":"INFO","category":"o.n.b.BoltServer","message":"Shutting down Bolt server"}
{"time":"2025-09-22 12:30:22.788+0000","level":"INFO","category":"o.n.b.p.c.c.ConnectionRegistry","message":"Stopping 0 connections for connector bolt-local"}
{"time":"2025-09-22 12:30:22.788+0000","level":"INFO","category":"o.n.b.p.c.c.ConnectionRegistry","message":"Stopped all remaining connections for connector bolt-local"}
{"time":"2025-09-22 12:30:22.788+0000","level":"INFO","category":"o.n.b.p.c.c.ConnectionRegistry","message":"Stopping 0 connections for connector bolt"}
{"time":"2025-09-22 12:30:22.788+0000","level":"INFO","category":"o.n.b.p.c.c.ConnectionRegistry","message":"Stopped all remaining connections for connector bolt"}
{"time":"2025-09-22 12:30:27.901+0000","level":"INFO","category":"o.n.b.BoltServer","message":"Bolt server has been shut down"}
