Neo4j
Copyright © 2002-2023 Neo4j Sweden AB (referred to in this notice as "Neo4j")
[https://neo4j.com]

This product includes software ("Software") developed by Neo4j.

The copyright in the bundled Neo4j graph database (including the
Software) is owned by Neo4j. The Software developed and owned
by Neo4j is licensed under the GNU GENERAL PUBLIC LICENSE Version 3
(http://www.fsf.org/licensing/licenses/gpl-3.0.html) ("GPL")
to all third parties and that license, as required by the GPL, is
included in the LICENSE.txt file.

However, if you have executed an End User Software License and Services
Agreement or an OEM Software License and Support Services Agreement, or
another commercial license agreement with Neo4j or one of its
affiliates (each, a "Commercial Agreement"), the terms of the license in
such Commercial Agreement will supersede the GPL and you may use the
software solely pursuant to the terms of the relevant Commercial
Agreement.

Full license texts are found in LICENSES.txt.


Third-party licenses
--------------------

Apache Software License, Version 2.0
  @firebase/app
  @firebase/app-types
  @firebase/auth
  @firebase/auth-types
  @firebase/database
  @firebase/database-types
  @firebase/firestore
  @firebase/firestore-types
  @firebase/functions
  @firebase/functions-types
  @firebase/logger
  @firebase/messaging
  @firebase/messaging-types
  @firebase/polyfill
  @firebase/storage
  @firebase/storage-types
  @firebase/util
  @firebase/webchannel-wrapper
  aircompressor
  Apache Commons Codec
  Apache Commons Compress
  Apache Commons Configuration
  Apache Commons IO
  Apache Commons Lang
  Apache Commons Logging
  Apache Commons Text
  Apache Log4j API
  Apache Log4j Core
  Apache Log4j Layout for JSON template
  Apache Lucene (module: backward-codecs)
  Apache Lucene (module: common)
  Apache Lucene (module: core)
  Apache Lucene (module: queryparser)
  Apache Parquet Column
  Apache Parquet Common
  Apache Parquet Encodings
  Apache Parquet Format Structures
  Apache Parquet Hadoop
  Apache Shiro :: Cache
  Apache Shiro :: Configuration :: Core
  Apache Shiro :: Core
  Apache Shiro :: Cryptography :: Ciphers
  Apache Shiro :: Cryptography :: Core
  Apache Shiro :: Cryptography :: Hashing
  Apache Shiro :: Event
  Apache Shiro :: Lang
  Arrow Flight Core
  Arrow Format
  Arrow Memory - Core
  Arrow Vectors
  ascli
  aws-sign2
  bytebuffer
  Caffeine cache
  caseless
  Core :: ALPN :: Java Server
  Core :: ALPN :: Server
  Core :: EE Common
  Core :: HTTP
  Core :: HTTP2 :: Common
  Core :: HTTP2 :: HPACK
  Core :: HTTP2 :: Server
  Core :: IO
  Core :: Security
  Core :: Server
  Core :: Sessions
  Core :: Utilities
  Core :: XML
  detect-libc
  disposables
  EE8 :: Nested
  EE8 :: Security
  EE8 :: Servlet
  EE8 :: WebApp
  fastinfoset
  FindBugs-jsr305
  firebase
  FlatBuffers Java API
  forever-agent
  grpc
  Guava InternalFutureFailureAccess and InternalFutures
  Guava ListenableFuture only
  Guava: Google Core Libraries for Java
  io.grpc:grpc-api
  io.grpc:grpc-context
  io.grpc:grpc-core
  io.grpc:grpc-netty
  io.grpc:grpc-protobuf
  io.grpc:grpc-stub
  IPAddress
  Jackson datatype: JSR310
  Jackson module: Old JAXB Annotations (javax.xml.bind)
  Jackson-annotations
  Jackson-core
  jackson-databind
  Jackson-JAXRS: base
  Jackson-JAXRS: JSON
  Jakarta Bean Validation API
  jansi
  Java Concurrency Tools Core Library
  Java Native Access
  Javassist
  jersey-container-servlet
  jersey-container-servlet-core
  jersey-core-client
  jersey-core-common
  jersey-core-server
  jersey-inject-hk2
  Jettison
  Jetty :: Servlet API and Schemas for JPMS and OSGi
  jPowerShell
  jProcesses
  long
  magnolia
  Neo4j Bolt Connection (Bolt Provider reference impl)
  Neo4j Bolt Connection (Pooled Source impl)
  Neo4j Bolt Connection (Provider SPI)
  Neo4j Bolt Connection (Routed Source impl)
  Neo4j Java Driver
  neo4j-driver
  Netty/Buffer
  Netty/Codec/Base
  Netty/Codec/Compression
  Netty/Codec/HTTP
  Netty/Codec/HTTP2
  Netty/Common
  Netty/Handler
  Netty/Resolver
  Netty/Transport
  Netty/Transport/Classes/Epoll
  Netty/Transport/Classes/KQueue
  Netty/Transport/Native/Unix/Common
  Non-Blocking Reactive Foundation for the JVM
  oauth-sign
  picocli
  proto-google-common-protos
  protobufjs
  request
  rxjs
  Scala Reflect
  scala-collection-contrib
  snappy-java
  Strategic Blue Parquet Floor
  tslib
  tunnel-agent
  WMI4Java

BSD - Scala License
  Scala Library

BSD License
  ANTLR 4 Runtime
  antlr4
  asm
  asm-analysis
  asm-tree
  asm-util
  bcrypt-pbkdf
  boom
  cryptiles
  D3.js
  dnd-core
  hawk
  hoek
  hoist-non-react-statics
  ieee754
  JLine JANSI Terminal
  JLine Reader
  JLine Terminal
  json-schema
  node-pre-gyp
  Protocol Buffers [Core]
  Protocol Buffers [Util]
  qs
  react-dnd
  react-dnd-html5-backend
  sntp
  tough-cookie
  Zstandard

BSD License 2-clause
  tar-pack
  uri-js
  zstd-jni

Common Development and Distribution License Version 1.1
  Java Architecture for XML Binding
  javax.annotation API

Eclipse Distribution License - v 1.0
  Eclipse Collections API
  Eclipse Collections Main Library
  Extended StAX API
  fastinfoset
  istack common utility code runtime
  Jakarta Activation API jar
  jakarta.xml.bind-api
  JAXB Runtime
  jersey-container-servlet
  jersey-container-servlet-core
  jersey-core-client
  jersey-inject-hk2
  TXW2 Runtime

Eclipse Public License - v 1.0
  Eclipse Collections API
  Eclipse Collections Main Library

Eclipse Public License v2.0
  HK2 API module
  HK2 Implementation Utilities
  Jakarta Annotations API
  jakarta.ws.rs-api
  javax.inject:1 as OSGi bundle
  jersey-container-servlet
  jersey-container-servlet-core
  jersey-core-client
  jersey-core-common
  jersey-core-server
  jersey-inject-hk2
  ServiceLocator Default Implementation

GNU General Public License, version 2 with the Classpath Exception
  HK2 API module
  HK2 Implementation Utilities
  Jakarta Annotations API
  jakarta.ws.rs-api
  Java Architecture for XML Binding
  javax.annotation API
  javax.inject:1 as OSGi bundle
  jersey-container-servlet
  jersey-container-servlet-core
  jersey-core-client
  jersey-core-common
  jersey-core-server
  jersey-inject-hk2
  ServiceLocator Default Implementation

ISC
  abbrev
  aproba
  are-we-there-yet
  block-stream
  cliui
  console-control-strings
  css-color-keywords
  fs.realpath
  fstream
  fstream-ignore
  gauge
  glob
  graceful-fs
  har-schema
  har-validator
  has-unicode
  inflight
  inherits
  ini
  json-stringify-safe
  minimatch
  nopt
  npmlog
  once
  osenv
  rimraf
  semver
  set-blocking
  signal-exit
  tar
  uid-number
  wide-align
  wrappy
  y18n

MIT License
  ajv
  ansi-regex
  argparse4j
  asap
  ascii-data-table
  asn1
  assert-plus
  assertion-error
  asynckit
  attr-accept
  aws4
  babel-runtime
  balanced-match
  base64-js
  bootstrap
  brace-expansion
  buffer
  camelcase
  canvg
  chai
  check-error
  classnames
  co
  code-point-at
  codemirror
  colour
  combined-stream
  concat-map
  core-js
  core-util-is
  css-to-react-native
  dashdash
  debug
  decamelize
  deep-eql
  deep-extend
  delayed-stream
  delegates
  dom-storage
  ecc-jsbn
  encoding
  extend
  extsprintf
  fast-deep-equal
  fast-json-stable-stringify
  faye-websocket
  fbjs
  file-saver
  Font Awesome CSS
  form-data
  fuzzaldrin
  get-func-name
  getpass
  has-flag
  http-parser-js
  http-signature
  iconv-lite
  invariant
  invert-kv
  is-fullwidth-code-point
  is-plain-object
  is-stream
  is-typedarray
  isarray
  isobject
  isomorphic-fetch
  isstream
  jersey-container-servlet
  jersey-container-servlet-core
  jersey-core-client
  jersey-inject-hk2
  js-tokens
  jsbn
  json-schema-traverse
  jsonic
  jsprim
  lcid
  lodash
  lodash-es
  lodash.debounce
  loose-envify
  mime-db
  mime-types
  minimist
  mkdirp
  ms
  nan
  node-fetch
  number-is-nan
  object-assign
  optjs
  os-homedir
  os-locale
  os-tmpdir
  path-is-absolute
  pathval
  performance-now
  postcss-value-parser
  process-nextick-args
  promise
  promise-polyfill
  prop-types
  punycode
  querystringify
  rc
  react
  react-addons-pure-render-mixin
  react-dom
  react-dropzone
  react-icon-base
  react-icons
  react-is
  react-redux
  react-suber
  react-timeago
  readable-stream
  redux
  redux-observable
  regenerator-runtime
  requires-port
  safe-buffer
  safer-buffer
  save-as
  setimmediate
  SLF4J API Module
  sshpk
  string-width
  string_decoder
  stringstream
  strip-ansi
  strip-json-comments
  styled-components
  stylis
  stylis-rule-sheet
  suber
  supports-color
  swipe-js-iso
  symbol-observable
  type-detect
  ua-parser-js
  url-parse
  util-deprecate
  uuid
  verror
  websocket-driver
  websocket-extensions
  whatwg-fetch
  window-size
  wrap-ansi
  xmlhttprequest
  yargs

MIT No Attribution License
  reactive-streams

Mozilla Public License, Version 2.0
  kiama

SIL OFL 1.1
  Font Awesome
  Inconsolata Font
  Open Sans Font

Unlicense
  jersey-container-servlet
  jersey-container-servlet-core
  jersey-core-client
  jersey-core-common
  jersey-inject-hk2
  tweetnacl

Dependencies with multiple licenses
-----------------------------------

Eclipse Collections API
  Eclipse Distribution License - v 1.0
  Eclipse Public License - v 1.0

Eclipse Collections Main Library
  Eclipse Distribution License - v 1.0
  Eclipse Public License - v 1.0

HK2 API module
  Eclipse Public License v2.0
  GNU General Public License, version 2 with the Classpath Exception

HK2 Implementation Utilities
  Eclipse Public License v2.0
  GNU General Public License, version 2 with the Classpath Exception

Jakarta Annotations API
  Eclipse Public License v2.0
  GNU General Public License, version 2 with the Classpath Exception

Java Architecture for XML Binding
  Common Development and Distribution License Version 1.1
  GNU General Public License, version 2 with the Classpath Exception

ServiceLocator Default Implementation
  Eclipse Public License v2.0
  GNU General Public License, version 2 with the Classpath Exception

fastinfoset
  Apache Software License, Version 2.0
  Eclipse Distribution License - v 1.0

jakarta.ws.rs-api
  Eclipse Public License v2.0
  GNU General Public License, version 2 with the Classpath Exception

javax.annotation API
  Common Development and Distribution License Version 1.1
  GNU General Public License, version 2 with the Classpath Exception

javax.inject:1 as OSGi bundle
  Eclipse Public License v2.0
  GNU General Public License, version 2 with the Classpath Exception

jersey-container-servlet
  Apache Software License, Version 2.0
  Eclipse Distribution License - v 1.0
  Eclipse Public License v2.0
  GNU General Public License, version 2 with the Classpath Exception
  MIT License
  Unlicense

jersey-container-servlet-core
  Apache Software License, Version 2.0
  Eclipse Distribution License - v 1.0
  Eclipse Public License v2.0
  GNU General Public License, version 2 with the Classpath Exception
  MIT License
  Unlicense

jersey-core-client
  Apache Software License, Version 2.0
  Eclipse Distribution License - v 1.0
  Eclipse Public License v2.0
  GNU General Public License, version 2 with the Classpath Exception
  MIT License
  Unlicense

jersey-core-common
  Apache Software License, Version 2.0
  Eclipse Public License v2.0
  GNU General Public License, version 2 with the Classpath Exception
  Unlicense

jersey-core-server
  Apache Software License, Version 2.0
  Eclipse Public License v2.0
  GNU General Public License, version 2 with the Classpath Exception

jersey-inject-hk2
  Apache Software License, Version 2.0
  Eclipse Distribution License - v 1.0
  Eclipse Public License v2.0
  GNU General Public License, version 2 with the Classpath Exception
  MIT License
  Unlicense

